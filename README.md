# amazonquick Insights & Comparison

A comprehensive Chrome extension that provides instant Amazon product insights and powerful comparison tools. Get detailed product information, reviews, and side-by-side comparisons directly on Amazon pages with hover-activated icons and right-click functionality.

## 🚀 Key Features

### **Smart Product Detection**
- **Auto-Hide Floating Icons**: Hover-activated icons appear on product images across all Amazon sections
- **Universal Compatibility**: Works on search results, product pages, recommendations, deals, and special sections
- **Global Amazon Support**: Compatible with all Amazon country domains (US, UK, DE, FR, JP, etc.)

### **Comprehensive Product Information**
- **Detailed Product Popup**: Click icons to view rich product information including:
  - Product title, images, and image magnification on hover
  - Current price, original price, and discount calculations
  - Star ratings and review counts
  - Prime eligibility and shipping information
  - Product specifications and features
  - Customer reviews (top-rated reviews displayed)
  - "About this item" section with full details

### **Advanced Comparison System**
- **Side-by-Side Comparison**: Compare up to 4 products simultaneously
- **Smart Comparison Table**: Unified interface showing prices, ratings, features, and reviews
- **Horizontal Scrolling**: Smooth navigation when comparing multiple products
- **Review Integration**: Top 5 customer reviews for each product in comparison
- **Add via Right-Click**: Right-click any product image or link to add to comparison

### **Enhanced User Experience**
- **Movable Popups**: Drag popups to preferred positions (no resize to maintain optimal size)
- **Performance Optimized**: Fast loading with retry mechanisms for failed requests
- **Fallback Data Extraction**: Shows available information even when full details can't be loaded
- **Buy Now Integration**: Direct purchase with Amazon affiliate support (affiliate tag: 'comproduct-20')

### **Special Section Coverage**
- **"Based on your recent views"** sections
- **"Deals on related products"** sections
- **"Customers who bought this item also bought"** sections
- **"4 stars and above"** sections
- **Best Sellers and recommendation carousels**
- **Sponsored products and search results**

## 📦 Installation

### **For Development/Testing:**
1. **Add Extension Icons** (optional - icons are included):
   - `icon16.png` (16x16 pixels)
   - `icon48.png` (48x48 pixels)
   - `icon128.png` (128x128 pixels)

2. **Load in Chrome**:
   - Open Chrome and navigate to `chrome://extensions/`
   - Enable "Developer mode" (toggle in top-right corner)
   - Click "Load unpacked" and select the extension folder
   - The extension will appear in your Chrome toolbar

### **For Chrome Web Store** (when published):
- Search for "amazonquick Insights & Comparison" in the Chrome Web Store
- Click "Add to Chrome" to install

## 🎯 How to Use

### **Basic Usage**
1. **Browse Amazon**: Visit any Amazon page (search results, product pages, recommendations)
2. **Hover Over Products**: Icons automatically appear when hovering over product images
3. **Click for Details**: Click the info icon to view comprehensive product information
4. **Drag Popups**: Move popups by dragging the header to your preferred position

### **Product Comparison**
1. **Add Products**:
   - Click the "Add to Compare" button in product popups, OR
   - Right-click any product image/link and select "Add to Product Comparison"
2. **View Comparison**: Click the "Compare Products" button that appears after adding items
3. **Navigate**: Use horizontal scrolling when comparing more than 2 products
4. **Remove Items**: Click the "×" button on any product in the comparison table

### **Advanced Features**
- **Image Magnification**: Hover over product images in popups for enlarged view
- **Buy Now**: Click "Buy Now" buttons for direct purchase (opens product page and adds to cart)
- **Review Reading**: Scroll through top customer reviews in both popups and comparisons
- **Section Coverage**: Icons work in all Amazon sections including deals, recommendations, and recent views

## 📁 Project Structure

```
amazonquick-insights-comparison/
├── manifest.json          # Extension configuration and permissions
├── background.js          # Service worker for API calls and context menus
├── content.js            # Main content script (icon injection, popups, comparison)
├── popup.html            # Extension popup interface
├── popup.js              # Extension popup functionality
├── styles.css            # Comprehensive styling for all UI elements
├── icons/                # Extension icons
│   ├── icon16.png        # 16x16 toolbar icon
│   ├── icon48.png        # 48x48 management page icon
│   └── icon128.png       # 128x128 Chrome Web Store icon
└── README.md             # This documentation
```

## 🛠️ Technical Details

### **Permissions Used**
- `activeTab`: Access current Amazon tab
- `scripting`: Inject content scripts
- `tabs`: Tab management for Buy Now functionality
- `webNavigation`: Detect page navigation
- `contextMenus`: Right-click "Add to Comparison" option

### **Supported Amazon Domains**
- amazon.com (US)
- amazon.co.uk (UK)
- amazon.ca (Canada)
- amazon.de (Germany)
- amazon.fr (France)
- amazon.it (Italy)
- amazon.es (Spain)
- amazon.co.jp (Japan)
- amazon.in (India)
- amazon.com.au (Australia)
- amazon.com.mx (Mexico)
- amazon.com.br (Brazil)
- amazon.nl (Netherlands)
- amazon.se (Sweden)
- amazon.pl (Poland)
- amazon.sg (Singapore)
- amazon.ae (UAE)
- amazon.sa (Saudi Arabia)
- amazon.eg (Egypt)
- amazon.com.tr (Turkey)
- amazon.cn (China)
- amazon.com.be (Belgium)
- amazon.be (Belgium)

### **Performance Features**
- Debounced icon injection for smooth performance
- Retry mechanisms for failed network requests
- Fallback data extraction when full details unavailable
- Efficient DOM traversal with multiple selector strategies
- Memory-conscious comparison list management

## 🔧 Development Notes

### **Adding Custom Icons**
1. Create square PNG images:
   - `icon16.png` (16×16 pixels) - Toolbar display
   - `icon48.png` (48×48 pixels) - Extension management
   - `icon128.png` (128×128 pixels) - Chrome Web Store
2. Place in the `icons/` folder
3. Ensure high contrast and clear visibility at small sizes

### **Customization Options**
- Modify `CONSTANTS.AFFILIATE_TAG` in content.js for different affiliate tracking
- Adjust `MAX_COMPARISON_REVIEWS` for review count in comparisons
- Update `HOVER_DELAY` for icon appearance timing
- Customize colors and styling in styles.css

## 📝 Version History

**v2.0** - Current Release
- Added product comparison functionality
- Implemented right-click context menu
- Enhanced error handling and retry mechanisms
- Improved performance with debounced operations
- Added support for all Amazon domains
- Implemented movable popups
- Added image magnification
- Enhanced special section targeting

**v1.0** - Initial Release
- Basic floating icon functionality
- Product information popups
- Amazon search results integration

## 🤝 Contributing

This extension is designed for personal use and learning. Feel free to fork and modify according to your needs.

## ⚠️ Disclaimer

This extension is not affiliated with Amazon. It's designed to enhance the browsing experience by providing quick access to product information. The affiliate functionality uses the tag 'comproduct-20' - modify as needed for your use case.
