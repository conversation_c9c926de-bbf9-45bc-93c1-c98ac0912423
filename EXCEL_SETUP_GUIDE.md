# Excel Data Management Guide for affliater Extension

This guide explains how to use the internal Excel data management feature to collect and export Amazon product data.

## How It Works

The **affliater** extension automatically saves product data to your browser's local storage when you click the "📊 Send to Sheet" button. You can then download this data as an Excel-compatible CSV file anytime.

## Features

### ✅ **Automatic Data Collection**
- Product data is saved locally in your browser
- No external services or API keys required
- Data persists across browser sessions
- Automatic duplicate detection by ASIN

### 📊 **Excel Export**
- Download data as CSV file (opens in Excel)
- Includes all product details and metadata
- Timestamped filename for easy organization
- Proper CSV formatting with escaped special characters

### 🔒 **Privacy & Security**
- All data stored locally in your browser
- No data sent to external servers
- Complete control over your data
- Easy to clear all data when needed

## Using the Extension

### Step 1: Collect Product Data
1. **Browse Amazon** - Go to any Amazon product page or search results
2. **Hover over product images** - Look for the floating info icon
3. **Click the info icon** - Opens detailed product information popup
4. **Click "📊 Send to Sheet"** - Saves the product data locally

### Step 2: Monitor Your Data
1. **Open extension popup** - Click the extension icon in your browser toolbar
2. **Check statistics** - See how many products you've saved and when last updated
3. **View status** - Green checkmark indicates data is ready for download

### Step 3: Download Excel File
1. **Click "📥 Download Excel"** - Generates and downloads CSV file
2. **Choose save location** - Browser will prompt you to save the file
3. **Open in Excel** - Double-click the downloaded file to open in Excel

### Step 4: Manage Your Data
- **Clear Data** - Click "🗑️ Clear Data" to remove all saved products
- **Confirmation** - Extension will ask for confirmation before clearing

## Data Structure

Each product entry includes:

| Column | Description |
|--------|-------------|
| Timestamp | When the product was saved |
| Product Title | Full product name |
| ASIN | Amazon Standard Identification Number |
| Current Price | Current selling price |
| Old Price | Original/crossed-out price |
| Discount | Discount percentage |
| Rating | Average customer rating |
| Review Count | Number of customer reviews |
| Prime Eligible | Whether product has Prime shipping |
| Product URL | Direct link to product page |
| About Item | Product description/features |
| Description | Detailed product description |
| Specifications | Technical specifications |
| Shipping | Shipping information |
| Image URL | Main product image link |
| Total Images | Number of product images |
| Top Review | Highest-rated customer review |

## Tips for Best Results

### 🎯 **Data Collection**
- **Save products systematically** - Use consistent criteria for saving
- **Check for duplicates** - Extension automatically updates existing products
- **Regular downloads** - Download data periodically to avoid loss

### 📈 **Excel Analysis**
- **Sort by price** - Find best deals and price ranges
- **Filter by rating** - Focus on highly-rated products
- **Group by category** - Organize products by type or brand
- **Create charts** - Visualize price trends and ratings

### 🔄 **Data Management**
- **Backup regularly** - Download and save Excel files
- **Clear old data** - Remove outdated products periodically
- **Multiple files** - Create separate files for different research projects

## Troubleshooting

### Common Issues:

1. **"No data to download" error**
   - Make sure you've saved at least one product
   - Check the product count in the extension popup

2. **Download not starting**
   - Check browser download permissions
   - Ensure popup blockers aren't interfering
   - Try clicking the download button again

3. **Data not saving**
   - Verify the "Send to Sheet" button shows success message
   - Check browser storage permissions
   - Try refreshing the Amazon page and trying again

4. **Excel file won't open**
   - File is in CSV format - should open in Excel automatically
   - Try right-clicking file and "Open with Excel"
   - Ensure you have Excel or compatible software installed

### Browser Compatibility:
- **Chrome** - Full support
- **Edge** - Full support
- **Firefox** - Limited support (may need manual CSV import)
- **Safari** - Limited support (may need manual CSV import)

## Data Privacy

- **Local Storage Only** - All data stays in your browser
- **No Cloud Sync** - Data doesn't sync across devices
- **Manual Export** - You control when and where data is exported
- **Easy Deletion** - Clear all data with one click

## Advanced Usage

### Multiple Research Projects:
1. Download current data as "Project1.csv"
2. Clear all data in extension
3. Start collecting for new project
4. Download as "Project2.csv"

### Data Analysis in Excel:
- Use pivot tables for category analysis
- Create price comparison charts
- Filter by Prime eligibility
- Sort by customer ratings

### Backup Strategy:
- Download weekly backups
- Save files with descriptive names
- Store in organized folder structure
- Consider cloud storage for important data

## Support

If you encounter any issues:
1. Check this guide for troubleshooting steps
2. Verify browser permissions are enabled
3. Try refreshing the Amazon page
4. Clear and restart data collection if needed

The Excel data management feature is designed to be simple, secure, and powerful for Amazon product research!
