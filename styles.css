/* Styles for Amazon Product Info Enhancer */

/* Screen reader only text for accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Enhanced notification styles */
.amazon-enhancer-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: linear-gradient(135deg, #ff9900, #ff7700);
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  z-index: 1000000;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  max-width: 350px;
  word-wrap: break-word;
  animation: slideInRight 0.3s ease-out;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Error message styling */
.amazon-enhancer-error {
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 6px;
  padding: 12px;
  margin: 10px 0;
  color: #856404;
  font-size: 14px;
  line-height: 1.4;
}

.amazon-enhancer-error p {
  margin: 0 0 8px 0;
}

.amazon-enhancer-error p:last-child {
  margin-bottom: 0;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Floating icon on product images */
.amazon-enhancer-icon-wrapper {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 9999;
  pointer-events: auto;
}

.amazon-enhancer-icon {
  width: 32px;
  height: 32px;
  background-color: rgba(255, 153, 0, 0.9); /* Amazon orange with transparency */
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: opacity 0.2s ease, transform 0.2s ease, background-color 0.2s ease;
  opacity: 0; /* Hidden by default, shown on hover */
  border: 2px solid white; /* White border to make it stand out */
}

.amazon-enhancer-icon:focus {
  outline: none;
  border-color: #0066cc;
  box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.3);
}

/* Show icon on hover over the parent container or wrapper */
.s-image-container:hover .amazon-enhancer-icon,
.a-section.aok-relative:hover .amazon-enhancer-icon,
.a-image-container:hover .amazon-enhancer-icon,
.octopus-pc-item-image-container:hover .amazon-enhancer-icon,
.amazon-enhancer-icon-wrapper:hover .amazon-enhancer-icon,
.amazon-enhancer-icon:hover,

/* Recommendation section hover triggers */
.a-carousel-container .s-image-container:hover .amazon-enhancer-icon,
.a-carousel-container .a-image-container:hover .amazon-enhancer-icon,
.recommendations-container .s-image-container:hover .amazon-enhancer-icon,
.recommendations-container .a-image-container:hover .amazon-enhancer-icon,
.a-carousel-viewport .s-image-container:hover .amazon-enhancer-icon,
.a-carousel-viewport .a-image-container:hover .amazon-enhancer-icon,

/* Specific recommendation section hover triggers */
.recommendations-carousel .s-image-container:hover .amazon-enhancer-icon,
.browsing-history-carousel .s-image-container:hover .amazon-enhancer-icon,
.similarities-widget .s-image-container:hover .amazon-enhancer-icon,
.customers-also-bought .s-image-container:hover .amazon-enhancer-icon,
.brand-carousel .s-image-container:hover .amazon-enhancer-icon,
.frequently-bought-brands .s-image-container:hover .amazon-enhancer-icon,
.gift-ideas-carousel .s-image-container:hover .amazon-enhancer-icon,
.personalized-gifts .s-image-container:hover .amazon-enhancer-icon,
.related-products .s-image-container:hover .amazon-enhancer-icon,
.product-similarities .s-image-container:hover .amazon-enhancer-icon,

/* "Deals on related products" section hover triggers */
.deals-shoveler .s-image-container:hover .amazon-enhancer-icon,
.deals-carousel .s-image-container:hover .amazon-enhancer-icon,
.related-deals .s-image-container:hover .amazon-enhancer-icon,
.deal-container .s-image-container:hover .amazon-enhancer-icon,
[data-testid="deals-shoveler"] .s-image-container:hover .amazon-enhancer-icon,
[data-testid="related-deals"] .s-image-container:hover .amazon-enhancer-icon,
.s-deals-container .s-image-container:hover .amazon-enhancer-icon,
.deals-grid .s-image-container:hover .amazon-enhancer-icon,

/* Additional "Based on your recent views" section hover triggers */
[data-testid="browsing-history"] .s-image-container:hover .amazon-enhancer-icon,
[data-testid="recent-views"] .s-image-container:hover .amazon-enhancer-icon,
.recent-views-carousel .s-image-container:hover .amazon-enhancer-icon,
.browsing-history-container .s-image-container:hover .amazon-enhancer-icon,
.viewed-products .s-image-container:hover .amazon-enhancer-icon,
.bestsellers-carousel .s-image-container:hover .amazon-enhancer-icon,
.best-sellers .s-image-container:hover .amazon-enhancer-icon,

/* Carousel card hover triggers */
.a-carousel-card:hover .amazon-enhancer-icon,
.a-carousel-container .a-carousel-card:hover .amazon-enhancer-icon,
.recommendations-container .a-cardui:hover .amazon-enhancer-icon,

/* Generic hover triggers for recommendation sections */
.a-carousel-card[data-asin]:hover .amazon-enhancer-icon,
.recommendation-card[data-asin]:hover .amazon-enhancer-icon,
.product-card[data-asin]:hover .amazon-enhancer-icon,
.item-card[data-asin]:hover .amazon-enhancer-icon {
  opacity: 1;
}

.amazon-enhancer-icon:hover {
  transform: scale(1.1);
  background-color: rgba(255, 153, 0, 1);
}

.amazon-enhancer-icon svg {
  width: 20px;
  height: 20px;
  color: white;
}

/* Product info popup */
#amazon-enhancer-popup {
  position: fixed;
  top: 15%;
  left: 15%;
  width: 500px;
  height: 600px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  z-index: 2147483647; /* Maximum z-index value to ensure it's above everything */
  overflow: hidden;
  cursor: default;
  border: 2px solid #FF9900; /* Orange border to match buy button */
  font-family: Arial, sans-serif;
}

@keyframes amazon-enhancer-fade-in {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.amazon-enhancer-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  background-color: #232f3e; /* Amazon dark blue */
  color: white;
  cursor: move;
  user-select: none;
  position: relative;
}

.amazon-enhancer-popup-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 280px;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.amazon-enhancer-popup-drag-hint {
  font-size: 11px;
  font-weight: 400;
  opacity: 0.8;
  font-style: italic;
}



.amazon-enhancer-close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  margin: 0;
  line-height: 1;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.amazon-enhancer-popup-content {
  padding: 15px;
  height: 540px; /* Fixed height: 600px total - 60px header */
  overflow-y: auto;
  overscroll-behavior: contain; /* Prevents scrolling the page when at the end of the popup content */
}

.amazon-enhancer-image-gallery {
  display: flex;
  overflow-x: auto;
  margin-bottom: 15px;
  padding-bottom: 5px;
  scrollbar-width: thin;
  position: relative; /* For positioning the magnified image */
}

.amazon-enhancer-image-gallery img {
  height: 120px;
  margin-right: 10px;
  border-radius: 4px;
  object-fit: contain;
  cursor: zoom-in;
  transition: transform 0.2s ease;
}

/* Image magnification */
.amazon-enhancer-image-gallery img:hover {
  transform: scale(1.05);
}

/* Image zoom modal */
#amazon-enhancer-zoom-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  z-index: 2147483647;
  display: none;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

#amazon-enhancer-zoom-modal.active {
  display: flex;
}

#amazon-enhancer-zoom-image {
  max-width: 90%;
  max-height: 80%;
  object-fit: contain;
}

#amazon-enhancer-zoom-close {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

#amazon-enhancer-zoom-close:hover {
  background-color: rgba(255, 255, 255, 0.4);
}

#amazon-enhancer-zoom-controls {
  display: flex;
  margin-top: 20px;
}

#amazon-enhancer-zoom-prev,
#amazon-enhancer-zoom-next {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  font-size: 20px;
  margin: 0 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

#amazon-enhancer-zoom-prev:hover,
#amazon-enhancer-zoom-next:hover {
  background-color: rgba(255, 255, 255, 0.4);
}

.amazon-enhancer-price-section {
  margin-bottom: 10px;
}

.amazon-enhancer-current-price {
  font-size: 18px;
  font-weight: bold;
  color: #B12704; /* Amazon price red */
}

.amazon-enhancer-old-price {
  font-size: 14px;
  text-decoration: line-through;
  color: #565959;
  margin-left: 8px;
}

.amazon-enhancer-rating-section {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  flex-wrap: wrap;
}

.amazon-enhancer-star-rating {
  display: flex;
  margin-right: 5px;
  font-size: 18px;
  line-height: 1;
}

.amazon-enhancer-star {
  margin-right: 2px;
}

.amazon-enhancer-star-full {
  color: #FFA41C; /* Amazon star color */
}

.amazon-enhancer-star-half {
  color: #FFA41C;
  position: relative;
}

.amazon-enhancer-star-empty {
  color: #E7E7E7;
}

.amazon-enhancer-rating-text {
  color: #FFA41C; /* Amazon star color */
  margin-right: 5px;
  font-weight: bold;
}

.amazon-enhancer-review-count {
  color: #007185; /* Amazon link blue */
  font-size: 14px;
}

.amazon-enhancer-prime-badge {
  display: inline-block;
  background-color: #00A8E1; /* Prime blue */
  color: white;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 3px;
  margin-bottom: 10px;
}

.amazon-enhancer-section {
  margin-top: 15px;
  padding-top: 10px;
  border-top: 1px solid #e7e7e7;
}

.amazon-enhancer-section h4 {
  font-size: 14px;
  font-weight: 700;
  margin: 0 0 8px 0;
  color: #0F1111;
}

.amazon-enhancer-shipping,
.amazon-enhancer-about-item,
.amazon-enhancer-description,
.amazon-enhancer-specifications,
.amazon-enhancer-product-details,
.amazon-enhancer-product-overview,
.amazon-enhancer-review-content {
  font-size: 14px;
  color: #565959;
  margin-bottom: 8px;
  line-height: 1.4;
}

.amazon-enhancer-about-item,
.amazon-enhancer-description,
.amazon-enhancer-specifications,
.amazon-enhancer-product-details,
.amazon-enhancer-product-overview,
.amazon-enhancer-reviews {
  padding-right: 5px;
}

/* Make sure all expanded content is visible */
.amazon-enhancer-about-item .a-expander-content,
.amazon-enhancer-description .a-expander-content,
.amazon-enhancer-specifications .a-expander-content,
.amazon-enhancer-product-details .a-expander-content,
.amazon-enhancer-product-overview .a-expander-content,
.amazon-enhancer-review-content .a-expander-content,
.amazon-enhancer-shipping .a-expander-content {
  display: block !important;
  max-height: none !important;
  height: auto !important;
  overflow: visible !important;
}

/* Hide all "see more" buttons and expander prompts */
.amazon-enhancer-about-item .a-expander-prompt,
.amazon-enhancer-description .a-expander-prompt,
.amazon-enhancer-specifications .a-expander-prompt,
.amazon-enhancer-product-details .a-expander-prompt,
.amazon-enhancer-product-overview .a-expander-prompt,
.amazon-enhancer-review-content .a-expander-prompt,
.amazon-enhancer-shipping .a-expander-prompt,
.amazon-enhancer-about-item .a-expander-header,
.amazon-enhancer-description .a-expander-header,
.amazon-enhancer-specifications .a-expander-header,
.amazon-enhancer-product-details .a-expander-header,
.amazon-enhancer-product-overview .a-expander-header,
.amazon-enhancer-review-content .a-expander-header,
.amazon-enhancer-shipping .a-expander-header {
  display: none !important;
}

/* Ensure expander containers show full content */
.amazon-enhancer-about-item .a-expander-container,
.amazon-enhancer-description .a-expander-container,
.amazon-enhancer-specifications .a-expander-container,
.amazon-enhancer-product-details .a-expander-container,
.amazon-enhancer-product-overview .a-expander-container,
.amazon-enhancer-review-content .a-expander-container,
.amazon-enhancer-shipping .a-expander-container {
  max-height: none !important;
  height: auto !important;
  overflow: visible !important;
}

/* Force all content to be visible without truncation */
.amazon-enhancer-about-item *,
.amazon-enhancer-description *,
.amazon-enhancer-specifications *,
.amazon-enhancer-product-details *,
.amazon-enhancer-product-overview *,
.amazon-enhancer-review-content *,
.amazon-enhancer-shipping * {
  max-height: none !important;
  overflow: visible !important;
  height: auto !important;
  white-space: normal !important;
  text-overflow: clip !important;
  -webkit-line-clamp: none !important;
  line-clamp: none !important;
  -webkit-box-orient: unset !important;
  display: block !important;
}

/* Override any Amazon-specific truncation classes */
.amazon-enhancer-about-item .a-truncate,
.amazon-enhancer-description .a-truncate,
.amazon-enhancer-specifications .a-truncate,
.amazon-enhancer-product-details .a-truncate,
.amazon-enhancer-product-overview .a-truncate,
.amazon-enhancer-review-content .a-truncate,
.amazon-enhancer-shipping .a-truncate {
  max-height: none !important;
  overflow: visible !important;
  white-space: normal !important;
  text-overflow: clip !important;
  -webkit-line-clamp: none !important;
  line-clamp: none !important;
  -webkit-box-orient: unset !important;
  display: block !important;
}

/* Hide any remaining "see more" or "read more" links */
.amazon-enhancer-about-item a[href*="see-more"],
.amazon-enhancer-description a[href*="see-more"],
.amazon-enhancer-specifications a[href*="see-more"],
.amazon-enhancer-product-details a[href*="see-more"],
.amazon-enhancer-product-overview a[href*="see-more"],
.amazon-enhancer-review-content a[href*="see-more"],
.amazon-enhancer-shipping a[href*="see-more"],
.amazon-enhancer-about-item a[href*="read-more"],
.amazon-enhancer-description a[href*="read-more"],
.amazon-enhancer-specifications a[href*="read-more"],
.amazon-enhancer-product-details a[href*="read-more"],
.amazon-enhancer-product-overview a[href*="read-more"],
.amazon-enhancer-review-content a[href*="read-more"],
.amazon-enhancer-shipping a[href*="read-more"],
.amazon-enhancer-about-item .a-expander-prompt,
.amazon-enhancer-description .a-expander-prompt,
.amazon-enhancer-specifications .a-expander-prompt,
.amazon-enhancer-product-details .a-expander-prompt,
.amazon-enhancer-product-overview .a-expander-prompt,
.amazon-enhancer-review-content .a-expander-prompt,
.amazon-enhancer-shipping .a-expander-prompt {
  display: none !important;
  visibility: hidden !important;
}

/* Reviews styling */
.amazon-enhancer-reviews {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.amazon-enhancer-review {
  padding: 10px;
  border-radius: 4px;
  background-color: #f7f7f7;
  border: 1px solid #e7e7e7;
}

.amazon-enhancer-review-header {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.amazon-enhancer-review-rating {
  color: #FFA41C;
  margin-right: 8px;
  font-weight: bold;
}

.amazon-enhancer-review-title {
  font-weight: bold;
  color: #0F1111;
}

.amazon-enhancer-review-meta {
  font-size: 12px;
  color: #565959;
  margin-bottom: 8px;
}

.amazon-enhancer-review-author {
  margin-right: 5px;
}

.amazon-enhancer-review-date {
  margin-right: 5px;
}

.amazon-enhancer-review-verified {
  color: #C45500;
  font-weight: bold;
}

.amazon-enhancer-review-content {
  font-size: 14px;
  line-height: 1.4;
}

.amazon-enhancer-discount {
  color: #B12704;
  font-weight: bold;
  margin-left: 8px;
  font-size: 14px;
}

.amazon-enhancer-buttons-section {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 10px;
  margin-top: 15px;
  margin-bottom: 15px;
}

/* Make sure buttons are properly sized in the buttons section */
.amazon-enhancer-buttons-row {
  display: flex;
  justify-content: space-between;
  gap: 10px;
  width: 100%;
}

.amazon-enhancer-buttons-row .amazon-enhancer-buy-button {
  flex: 1;
  min-width: 100px;
}





.amazon-enhancer-buy-button {
  flex: 1;
  display: block !important;
  background-color: #FF9900 !important;
  border: 1px solid #FF8000 !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 5px rgba(213, 217, 217, .5) !important;
  color: white !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  text-align: center !important;
  text-decoration: none !important;
  padding: 8px 10px !important;
  margin: 10px 0 !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  z-index: 100 !important;
  width: auto !important;
  min-width: 120px !important;
  max-width: none !important;
  height: auto !important;
  line-height: normal !important;
  float: none !important;
  position: static !important;
  opacity: 1 !important;
  visibility: visible !important;
}

.amazon-enhancer-buy-button:hover {
  background-color: #FF8000 !important;
  border-color: #FF6600 !important;
  text-decoration: none !important;
  color: white !important;
}

.amazon-enhancer-buy-button:disabled {
  opacity: 0.7 !important;
  cursor: default !important;
}

/* Loading indicators */
.amazon-enhancer-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 0;
}

.amazon-enhancer-loading-details {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px 0;
  margin-top: 15px;
  border-top: 1px solid #e7e7e7;
}

.amazon-enhancer-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 153, 0, 0.2);
  border-radius: 50%;
  border-top-color: #ff9900;
  animation: amazon-enhancer-spin 1s linear infinite;
  margin-bottom: 15px;
}

.amazon-enhancer-spinner-small {
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 153, 0, 0.2);
  border-radius: 50%;
  border-top-color: #ff9900;
  animation: amazon-enhancer-spin 1s linear infinite;
  margin-right: 10px;
}

@keyframes amazon-enhancer-spin {
  to { transform: rotate(360deg); }
}



/* Ensure buttons work properly in popup */
.amazon-enhancer-popup-content .amazon-enhancer-buttons-row {
  display: flex;
  gap: 10px;
  align-items: center;
}











/* Scrollbar styling */
.amazon-enhancer-popup-content::-webkit-scrollbar,
.amazon-enhancer-image-gallery::-webkit-scrollbar,
.amazon-enhancer-about-item::-webkit-scrollbar,
.amazon-enhancer-description::-webkit-scrollbar,
.amazon-enhancer-specifications::-webkit-scrollbar,
.amazon-enhancer-product-details::-webkit-scrollbar,
.amazon-enhancer-product-overview::-webkit-scrollbar,
.amazon-enhancer-reviews::-webkit-scrollbar {
  width: 12px; /* Vertical scrollbar */
  height: 12px; /* Horizontal scrollbar */
}



.amazon-enhancer-popup-content::-webkit-scrollbar-track,
.amazon-enhancer-image-gallery::-webkit-scrollbar-track,
.amazon-enhancer-about-item::-webkit-scrollbar-track,
.amazon-enhancer-description::-webkit-scrollbar-track,
.amazon-enhancer-specifications::-webkit-scrollbar-track,
.amazon-enhancer-product-details::-webkit-scrollbar-track,
.amazon-enhancer-product-overview::-webkit-scrollbar-track,
.amazon-enhancer-reviews::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.amazon-enhancer-popup-content::-webkit-scrollbar-thumb,
.amazon-enhancer-image-gallery::-webkit-scrollbar-thumb,
.amazon-enhancer-about-item::-webkit-scrollbar-thumb,
.amazon-enhancer-description::-webkit-scrollbar-thumb,
.amazon-enhancer-specifications::-webkit-scrollbar-thumb,
.amazon-enhancer-product-details::-webkit-scrollbar-thumb,
.amazon-enhancer-product-overview::-webkit-scrollbar-thumb,
.amazon-enhancer-reviews::-webkit-scrollbar-thumb {
  background: #4CAF50;
  border-radius: 4px;
}

.amazon-enhancer-popup-content::-webkit-scrollbar-thumb:hover,
.amazon-enhancer-image-gallery::-webkit-scrollbar-thumb:hover,
.amazon-enhancer-about-item::-webkit-scrollbar-thumb:hover,
.amazon-enhancer-description::-webkit-scrollbar-thumb:hover,
.amazon-enhancer-specifications::-webkit-scrollbar-thumb:hover,
.amazon-enhancer-product-details::-webkit-scrollbar-thumb:hover,
.amazon-enhancer-product-overview::-webkit-scrollbar-thumb:hover,
.amazon-enhancer-reviews::-webkit-scrollbar-thumb:hover {
  background: #388E3C;
}
