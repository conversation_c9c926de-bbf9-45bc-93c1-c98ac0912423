# Google Sheets Setup Guide for affliater Extension

This guide will help you set up Google Sheets integration to automatically save Amazon product data.

## Prerequisites

1. A Google account
2. Access to Google Sheets
3. Google Cloud Console access (free)

## Step 1: Create a Google Sheet

1. Go to [Google Sheets](https://sheets.google.com)
2. Create a new spreadsheet
3. Name it something like "Amazon Products Data"
4. Copy the Sheet ID from the URL:
   - URL format: `https://docs.google.com/spreadsheets/d/SHEET_ID_HERE/edit`
   - Copy the `SHEET_ID_HERE` part

## Step 2: Get Google Sheets API Key

1. Go to [Google Cloud Console](https://console.cloud.google.com)
2. Create a new project or select an existing one
3. Enable the Google Sheets API:
   - Go to "APIs & Services" > "Library"
   - Search for "Google Sheets API"
   - Click on it and press "Enable"
4. Create an API Key:
   - Go to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "API Key"
   - Copy the generated API key

## Step 3: Make Your Sheet Public (Read/Write)

1. Open your Google Sheet
2. Click "Share" button (top right)
3. Click "Change to anyone with the link"
4. Set permission to "Editor"
5. Click "Done"

**Important**: This makes your sheet publicly editable. Only use this for non-sensitive data.

## Step 4: Configure the Extension

1. Open the extension files
2. Edit `background.js`
3. Find the `GOOGLE_SHEETS_CONFIG` section at the top
4. Replace the placeholder values:

```javascript
const GOOGLE_SHEETS_CONFIG = {
  SHEET_ID: 'YOUR_ACTUAL_SHEET_ID_HERE', // Replace with your Sheet ID
  API_KEY: 'YOUR_ACTUAL_API_KEY_HERE',   // Replace with your API Key
  RANGE: 'Sheet1!A:R',                   // Keep as is (or adjust if needed)
  SHEET_NAME: 'Sheet1'                   // Keep as is (or change sheet name)
};
```

## Step 5: Initialize Headers (Optional)

The extension will automatically create headers when you send your first product. The headers will be:

- Timestamp
- Product Title
- ASIN
- Current Price
- Old Price
- Discount
- Rating
- Review Count
- Prime Eligible
- Product URL
- About Item
- Description
- Specifications
- Shipping
- Image URL
- Total Images
- Top Review

## Step 6: Test the Integration

1. Reload the extension in Chrome
2. Go to any Amazon product page
3. Hover over a product image to see the info icon
4. Click the icon to open the product popup
5. Click "📊 Send to Sheet" button
6. Check your Google Sheet for the new data

## Troubleshooting

### Common Issues:

1. **"Google Sheets not configured" error**
   - Make sure you replaced the placeholder values in `background.js`
   - Reload the extension after making changes

2. **"Permission denied" error**
   - Ensure your Google Sheet is set to "Anyone with the link can edit"
   - Check that the Google Sheets API is enabled in your Google Cloud project

3. **"Invalid API key" error**
   - Verify your API key is correct
   - Make sure the Google Sheets API is enabled for your project

4. **Data not appearing in sheet**
   - Check the browser console for error messages
   - Verify the Sheet ID is correct
   - Ensure the sheet name matches the configuration

### Security Notes:

- The API key and Sheet ID are stored in the extension files
- Anyone with access to your extension files can see these credentials
- Consider using a dedicated Google account for this purpose
- Regularly monitor your Google Cloud usage

## Data Format

Each product will create a new row with the following information:
- Timestamp of when the data was saved
- Complete product details including title, ASIN, pricing, ratings
- Product specifications and descriptions (truncated to 500 characters)
- Top customer review (truncated to 200 characters)
- Product images and metadata

## Advanced Configuration

You can modify the data format by editing the `prepareSheetData` function in `content.js` and the corresponding Google Sheets setup.
