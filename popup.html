<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>affliater</title>
  <style>
    body {
      width: 350px;
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
    }

    .popup-header {
      background-color: #232f3e;
      color: white;
      padding: 12px 15px;
      text-align: center;
    }

    .popup-header h1 {
      margin: 0;
      font-size: 16px;
    }

    .popup-content {
      padding: 15px;
    }

    .status-message {
      font-size: 14px;
      line-height: 1.4;
      color: #333;
    }



    .feature-list {
      margin-top: 15px;
      padding-left: 20px;
      font-size: 13px;
    }

    .feature-list li {
      margin-bottom: 5px;
    }

    .footer {
      font-size: 11px;
      color: #666;
      text-align: center;
      margin-top: 15px;
      padding-top: 10px;
      border-top: 1px solid #eee;
    }

    /* Excel Configuration Section Styles */
    .config-section {
      margin-top: 20px;
      padding: 15px;
      background-color: #f8f9fa;
      border-radius: 8px;
      border: 1px solid #e0e0e0;
    }

    .config-section h3 {
      margin: 0 0 15px 0;
      font-size: 16px;
      color: #333;
    }

    .config-status {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 15px;
      padding: 8px 12px;
      background-color: white;
      border-radius: 6px;
      border: 1px solid #ddd;
    }

    .status-indicator {
      font-size: 16px;
    }

    .excel-stats {
      margin-bottom: 15px;
    }

    .stat-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 6px 0;
      border-bottom: 1px solid #eee;
    }

    .stat-item:last-child {
      border-bottom: none;
    }

    .stat-label {
      font-size: 12px;
      color: #666;
      font-weight: bold;
    }

    .stat-value {
      font-size: 12px;
      color: #333;
      font-weight: bold;
    }

    .excel-buttons {
      display: flex;
      gap: 8px;
      margin-bottom: 10px;
    }

    .download-btn, .clear-btn {
      flex: 1;
      padding: 8px 12px;
      border: none;
      border-radius: 4px;
      font-size: 11px;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .download-btn {
      background-color: #4CAF50;
      color: white;
    }

    .download-btn:hover {
      background-color: #45a049;
    }

    .clear-btn {
      background-color: #f44336;
      color: white;
    }

    .clear-btn:hover {
      background-color: #da190b;
    }

    .download-btn:disabled, .clear-btn:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
    }

    .help-text {
      text-align: center;
      margin-top: 10px;
    }

    .help-text small {
      color: #666;
      font-size: 10px;
      line-height: 1.3;
    }
  </style>
</head>
<body>
  <div class="popup-header">
    <h1>affliater</h1>
  </div>

  <div class="popup-content">
    <div class="status-message">
      <p id="status-text">Browse Amazon product listings to see enhanced product information.</p>
    </div>

    <ul class="feature-list">
      <li>Hover over product images to see the info icon</li>
      <li>Click the icon to view detailed product information</li>
      <li>Use "Send to Sheet" to save data to Excel file</li>
    </ul>

    <!-- Excel Data Management Section -->
    <div class="config-section">
      <h3>📊 Excel Data Management</h3>
      <div class="config-status" id="config-status">
        <span class="status-indicator" id="status-indicator">📋</span>
        <span id="status-message">Ready to collect data</span>
      </div>

      <div class="excel-stats" id="excel-stats">
        <div class="stat-item">
          <span class="stat-label">Products Saved:</span>
          <span class="stat-value" id="product-count">0</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">Last Updated:</span>
          <span class="stat-value" id="last-updated">Never</span>
        </div>
      </div>

      <div class="excel-buttons">
        <button type="button" id="download-excel" class="download-btn">📥 Download Excel</button>
        <button type="button" id="clear-data" class="clear-btn">🗑️ Clear Data</button>
      </div>

      <div class="help-text">
        <small>Product data is automatically saved when you click "Send to Sheet". Download your Excel file anytime!</small>
      </div>
    </div>

    <div class="footer">
      affliater v2.0
    </div>
  </div>

  <script src="popup.js"></script>
</body>
</html>
