<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>amazonquick Insights & Comparison</title>
  <style>
    body {
      width: 300px;
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
    }

    .popup-header {
      background-color: #232f3e;
      color: white;
      padding: 12px 15px;
      text-align: center;
    }

    .popup-header h1 {
      margin: 0;
      font-size: 16px;
    }

    .popup-content {
      padding: 15px;
    }

    .status-message {
      font-size: 14px;
      line-height: 1.4;
      color: #333;
    }



    .feature-list {
      margin-top: 15px;
      padding-left: 20px;
      font-size: 13px;
    }

    .feature-list li {
      margin-bottom: 5px;
    }

    .footer {
      font-size: 11px;
      color: #666;
      text-align: center;
      margin-top: 15px;
      padding-top: 10px;
      border-top: 1px solid #eee;
    }
  </style>
</head>
<body>
  <div class="popup-header">
    <h1>amazonquick Insights & Comparison</h1>
  </div>

  <div class="popup-content">
    <div class="status-message">
      <p id="status-text">Browse Amazon product listings to see enhanced product information.</p>
    </div>

    <ul class="feature-list">
      <li>Hover over product images to see the info icon</li>
      <li>Click the icon to view detailed product information</li>
      <li>See prices, ratings, images, and more at a glance</li>
    </ul>

    <div class="footer">
      amazonquick Insights & Comparison v1.0
    </div>
  </div>

  <script src="popup.js"></script>
</body>
</html>
