// Content script for amazonquick Insights
'use strict';

// Early validation function
(function() {
  try {
    // Check if we're on a restricted page
    const currentUrl = window.location.href;
    const restrictedSchemes = ['chrome:', 'chrome-extension:', 'moz-extension:', 'edge:', 'about:'];

    if (restrictedSchemes.some(scheme => currentUrl.startsWith(scheme))) {
      console.debug('amazonquick Insights: Skipping restricted page:', currentUrl);
      return false;
    }

    // Check if we're on an Amazon domain
    const hostname = window.location.hostname.toLowerCase();
    const amazonDomains = [
      'amazon.com', 'amazon.co.uk', 'amazon.ca', 'amazon.de', 'amazon.fr',
      'amazon.it', 'amazon.es', 'amazon.co.jp', 'amazon.in', 'amazon.com.au',
      'amazon.com.mx', 'amazon.com.br', 'amazon.nl', 'amazon.se', 'amazon.pl',
      'amazon.sg', 'amazon.ae', 'amazon.sa', 'amazon.eg', 'amazon.com.tr',
      'amazon.cn', 'amazon.com.be', 'amazon.be'
    ];

    if (!amazonDomains.some(domain => hostname.includes(domain))) {
      console.debug('amazonquick Insights & Comparison: Not on Amazon domain:', hostname);
      return false;
    }

    return true;
  } catch (error) {
    console.error('amazonquick Insights & Comparison: Error during initial validation:', error);
    return false;
  }
})() && (function() {

// Prevent multiple initializations
if (typeof window.amazonEnhancerInitialized === 'undefined') {
  window.amazonEnhancerInitialized = true;

  // Constants - protected inside initialization guard
  const CONSTANTS = {
    EXTENSION_NAME: 'amazonquick Insights',
    MAX_REVIEWS: 6,

    MAX_IMAGES: 10,
    POPUP_Z_INDEX: 999999,
    ICON_Z_INDEX: 9999,
    HOVER_DELAY: 100,
    FETCH_TIMEOUT: 10000,
    AFFILIATE_TAG: 'comproduct-20',

    SELECTORS: {
      PRODUCT_CONTAINERS: [
        // Search results and listings
        '.s-result-item',
        '.a-cardui',
        '.octopus-pc-card',
        '.p13n-sc-uncoverable-faceout',
        '.zg-item-immersion',
        '.zg_item',
        '.dealContainer',
        '.a-carousel-card',
        '[data-component-type="s-search-result"]',
        '.a-section[data-asin]',
        '.sg-col-inner .s-widget-container',
        '.rush-component[data-asin]',

        // Recommendation sections
        '.a-carousel-container .a-carousel-card',
        '.a-carousel-container [data-asin]',
        '.recommendations-container .a-cardui',
        '.recommendations-container [data-asin]',
        '.a-section.a-spacing-base[data-asin]',
        '.a-section.a-spacing-small[data-asin]',

        // "4 stars and above" section
        '.s-result-list .s-result-item',
        '.s-search-results .s-result-item',
        '.s-main-slot .s-result-item',

        // "Based on your recent views" section
        '.a-carousel-viewport .a-carousel-card',
        '.recommendations-carousel .a-carousel-card',
        '.browsing-history-carousel .a-carousel-card',
        '[data-component-type="sp-sponsored-result"]',
        '[data-component-type="s-sponsored-result"]',

        // "Deals on related products" section
        '.deals-shoveler .a-carousel-card',
        '.deals-carousel .a-carousel-card',
        '.related-deals .a-carousel-card',
        '.deal-container .a-carousel-card',
        '[data-testid="deals-shoveler"] .a-carousel-card',
        '[data-testid="related-deals"] .a-carousel-card',
        '.s-deals-container .a-carousel-card',
        '.deals-grid .a-carousel-card',

        // Additional "Based on your recent views" selectors
        '[data-testid="browsing-history"] .a-carousel-card',
        '[data-testid="recent-views"] .a-carousel-card',
        '.recent-views-carousel .a-carousel-card',
        '.browsing-history-container .a-carousel-card',
        '.viewed-products .a-carousel-card',

        // "Customers who bought this item also bought" section
        '.a-carousel-container[data-a-carousel-options] .a-carousel-card',
        '.similarities-widget .a-carousel-card',
        '.customers-also-bought .a-carousel-card',

        // "More from frequently bought brands" section
        '.brand-carousel .a-carousel-card',
        '.frequently-bought-brands .a-carousel-card',
        '.brand-recommendations .a-carousel-card',

        // "Gift ideas inspired by your shopping history" section
        '.gift-ideas-carousel .a-carousel-card',
        '.personalized-gifts .a-carousel-card',
        '.shopping-history-gifts .a-carousel-card',

        // "Products related to this item" section
        '.related-products .a-carousel-card',
        '.product-similarities .a-carousel-card',
        '.related-items-carousel .a-carousel-card',

        // "Best Sellers" section
        '.bestsellers-carousel .a-carousel-card',
        '.best-sellers .a-carousel-card',
        '.top-rated-carousel .a-carousel-card',

        // Generic recommendation containers
        '.a-carousel-card[data-asin]',
        '.recommendation-card[data-asin]',
        '.product-card[data-asin]',
        '.item-card[data-asin]',

        // Generic fallbacks
        '[data-uuid]',
        '[data-index]',
        '.a-link-normal[href*="/dp/"]',
        '.a-link-normal[href*="/gp/product/"]',

        // Additional fallback selectors for recommendation sections
        '[data-component-type*="deals"]',
        '[data-component-type*="browsing"]',
        '[data-component-type*="recent"]',
        '[data-component-type*="viewed"]',
        '[data-component-type*="related"]',
        '.s-widget[data-component-type] .a-carousel-card',
        '.s-widget-container .a-carousel-card',
        '[data-cel-widget*="recent"] .a-carousel-card',
        '[data-cel-widget*="deals"] .a-carousel-card',
        '[data-cel-widget*="browsing"] .a-carousel-card',

        // Ultra-specific selectors for "Based on your recent views"
        '[data-testid*="RecentlyViewedProducts"] .a-carousel-card',
        '[data-testid*="recentlyViewed"] .a-carousel-card',
        '[data-testid*="recently-viewed"] .a-carousel-card',
        '[data-testid*="browsing-history"] .a-carousel-card',
        '[data-testid*="recent-history"] .a-carousel-card',
        '[data-cel-widget*="RecentlyViewedProducts"] .a-carousel-card',
        '[data-cel-widget*="recently_viewed"] .a-carousel-card',
        '[data-cel-widget*="browsing_history"] .a-carousel-card',

        // Ultra-specific selectors for "Deals on related products"
        '[data-testid*="DealsWidget"] .a-carousel-card',
        '[data-testid*="deals-widget"] .a-carousel-card',
        '[data-testid*="related-deals"] .a-carousel-card',
        '[data-testid*="RelatedDeals"] .a-carousel-card',
        '[data-cel-widget*="DealsWidget"] .a-carousel-card',
        '[data-cel-widget*="deals_widget"] .a-carousel-card',
        '[data-cel-widget*="related_deals"] .a-carousel-card',

        // Widget-based selectors
        '.s-widget[cel_widget_id*="recent"] .a-carousel-card',
        '.s-widget[cel_widget_id*="deals"] .a-carousel-card',
        '.s-widget[cel_widget_id*="browsing"] .a-carousel-card',
        '.s-widget[cel_widget_id*="viewed"] .a-carousel-card'
      ],
      IMAGE_CONTAINERS: [
        // Primary image containers
        '.s-image-container',
        '.a-section.aok-relative',
        '.a-image-container',
        '.octopus-pc-item-image-container',
        '.p13n-sc-uncoverable-faceout img',
        '.zg-item img',
        '.dealContainer img',
        '.a-carousel-card img',
        '.rush-component img',

        // Recommendation section image containers
        '.a-carousel-container .s-image-container',
        '.a-carousel-container .a-image-container',
        '.recommendations-container .s-image-container',
        '.recommendations-container .a-image-container',
        '.a-carousel-viewport .s-image-container',
        '.a-carousel-viewport .a-image-container',

        // Specific recommendation section images
        '.recommendations-carousel .s-image-container',
        '.browsing-history-carousel .s-image-container',
        '.similarities-widget .s-image-container',
        '.customers-also-bought .s-image-container',
        '.brand-carousel .s-image-container',
        '.frequently-bought-brands .s-image-container',
        '.gift-ideas-carousel .s-image-container',
        '.personalized-gifts .s-image-container',
        '.related-products .s-image-container',
        '.product-similarities .s-image-container',
        '.bestsellers-carousel .s-image-container',
        '.best-sellers .s-image-container',

        // "Deals on related products" section images
        '.deals-shoveler .s-image-container',
        '.deals-carousel .s-image-container',
        '.related-deals .s-image-container',
        '.deal-container .s-image-container',
        '[data-testid="deals-shoveler"] .s-image-container',
        '[data-testid="related-deals"] .s-image-container',
        '.s-deals-container .s-image-container',
        '.deals-grid .s-image-container',

        // Additional "Based on your recent views" section images
        '[data-testid="browsing-history"] .s-image-container',
        '[data-testid="recent-views"] .s-image-container',
        '.recent-views-carousel .s-image-container',
        '.browsing-history-container .s-image-container',
        '.viewed-products .s-image-container',

        // Generic image selectors
        'img[data-src]',
        'img[src*="images-amazon"]',
        'img[src*="ssl-images-amazon"]',
        'img[src*="m.media-amazon"]',
        '.a-link-normal img',
        '.s-image',
        '.a-dynamic-image'
      ]
    }
  };

  // List of all Amazon domains for global support
  const AMAZON_DOMAINS = Object.freeze([
    'amazon.com', 'amazon.co.uk', 'amazon.ca', 'amazon.de', 'amazon.fr',
    'amazon.it', 'amazon.es', 'amazon.co.jp', 'amazon.in', 'amazon.com.au',
    'amazon.com.mx', 'amazon.com.br', 'amazon.nl', 'amazon.se', 'amazon.pl',
    'amazon.sg', 'amazon.ae', 'amazon.sa', 'amazon.eg', 'amazon.com.tr',
    'amazon.cn', 'amazon.com.be', 'amazon.be'
  ]);

  // Safe Chrome runtime message sender with error handling
  function safeSendMessage(message, callback) {
    try {
      // Check if chrome.runtime is available and not invalidated
      if (typeof chrome !== 'undefined' &&
          chrome.runtime &&
          chrome.runtime.sendMessage &&
          chrome.runtime.id) {

        // Additional check for runtime validity
        try {
          chrome.runtime.sendMessage(message, (response) => {
            if (chrome.runtime.lastError) {
              const errorMessage = chrome.runtime.lastError.message;
              console.debug(`${CONSTANTS.EXTENSION_NAME}: Chrome runtime error:`, errorMessage);
              if (callback && typeof callback === 'function') {
                callback({ success: false, error: errorMessage });
              }
            } else if (callback && typeof callback === 'function') {
              callback(response);
            }
          });
        } catch (sendError) {
          console.debug(`${CONSTANTS.EXTENSION_NAME}: Error sending message:`, sendError.message);
          if (callback && typeof callback === 'function') {
            callback({ success: false, error: sendError.message || 'Message send failed' });
          }
        }
      } else {
        // Runtime not available - this is normal for content scripts in some contexts
        console.debug(`${CONSTANTS.EXTENSION_NAME}: Chrome runtime not available for message:`, message.action || 'unknown');
        if (callback && typeof callback === 'function') {
          callback({ success: false, error: 'Chrome runtime not available' });
        }
      }
    } catch (error) {
      console.debug(`${CONSTANTS.EXTENSION_NAME}: Exception in safeSendMessage:`, error.message || error);
      if (callback && typeof callback === 'function') {
        callback({ success: false, error: error.message || 'Unknown error' });
      }
    }
  }

  // Utility functions
  const Utils = {
    // Function to check if current page is on an Amazon domain
    isAmazonDomain() {
      try {
        const hostname = window.location.hostname.toLowerCase();
        return AMAZON_DOMAINS.some(domain => hostname.includes(domain));
      } catch (error) {
        console.error(`${CONSTANTS.EXTENSION_NAME}: Error checking Amazon domain:`, error);
        return false;
      }
    },

    // Debounce function for performance optimization
    debounce(func, wait) {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    },

    // Throttle function for performance optimization
    throttle(func, limit) {
      let inThrottle;
      return function(...args) {
        if (!inThrottle) {
          func.apply(this, args);
          inThrottle = true;
          setTimeout(() => inThrottle = false, limit);
        }
      };
    },

    // Safe element query selector with error handling
    safeQuerySelector(element, selector) {
      try {
        return element ? element.querySelector(selector) : null;
      } catch (error) {
        console.error(`${CONSTANTS.EXTENSION_NAME}: Error in querySelector:`, error);
        return null;
      }
    },

    // Safe element query selector all with error handling
    safeQuerySelectorAll(element, selector) {
      try {
        return element ? Array.from(element.querySelectorAll(selector)) : [];
      } catch (error) {
        console.error(`${CONSTANTS.EXTENSION_NAME}: Error in querySelectorAll:`, error);
        return [];
      }
    },

    // Clean and validate text content
    cleanText(text) {
      if (!text || typeof text !== 'string') return '';
      return text.trim().replace(/\s+/g, ' ').substring(0, 5000); // Limit length
    },

    // Validate URL
    isValidUrl(string) {
      try {
        new URL(string);
        return true;
      } catch (_) {
        return false;
      }
    },

    // Generate unique ID
    generateId() {
      return `amazon-enhancer-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
  };





  // Enhanced page detection with better selectors for all Amazon pages with products
  function isProductListingPage() {
    try {
      // Amazon search results page patterns
      const urlPatterns = [
        '/s?', '/s/', '/gp/bestsellers', '/gp/new-releases',
        '/gp/movers-and-shakers', '/gp/most-wished-for',
        '/b/', '/l/', '/n/', '/dp/', '/gp/product/'
      ];

      const currentUrl = window.location.href.toLowerCase();
      if (urlPatterns.some(pattern => currentUrl.includes(pattern))) {
        return true;
      }

      // Check for product grid elements that typically appear on listing pages
      const productGridSelectors = [
        '.s-result-list', '.s-search-results', '.s-main-slot',
        '.octopus-pc-asin-title', '.zg-item-immersion',
        '.p13n-sc-uncoverable-faceout', '.a-carousel-container'
      ];

      // Check for recommendation sections (these appear on product detail pages)
      const recommendationSelectors = [
        '.a-carousel-container',
        '.recommendations-container',
        '.a-carousel-viewport',
        '.similarities-widget',
        '.customers-also-bought',
        '.brand-carousel',
        '.frequently-bought-brands',
        '.gift-ideas-carousel',
        '.personalized-gifts',
        '.related-products',
        '.product-similarities',
        '.bestsellers-carousel',
        '.best-sellers'
      ];

      // Check if any product grids or recommendation sections exist
      const hasProductGrids = productGridSelectors.some(selector => {
        const elements = Utils.safeQuerySelectorAll(document, selector);
        return elements.length > 0;
      });

      const hasRecommendations = recommendationSelectors.some(selector => {
        const elements = Utils.safeQuerySelectorAll(document, selector);
        return elements.length > 0;
      });

      return hasProductGrids || hasRecommendations;
    } catch (error) {
      console.error(`${CONSTANTS.EXTENSION_NAME}: Error detecting product listing page:`, error);
      return false;
    }
  }

  // Main function to initialize the extension with enhanced error handling
  function initAmazonEnhancer() {
    try {
      // Prevent multiple initializations
      if (window.amazonEnhancerRunning) {
        console.log(`${CONSTANTS.EXTENSION_NAME}: Already running, skipping initialization`);
        return;
      }

      // Check if we're on an Amazon domain first
      if (!Utils.isAmazonDomain()) {
        console.log(`${CONSTANTS.EXTENSION_NAME}: Not on Amazon domain, skipping initialization`);
        return;
      }

      window.amazonEnhancerRunning = true;
      console.log(`${CONSTANTS.EXTENSION_NAME}: Initializing on`, window.location.hostname);



      // Check if we're on an Amazon product listing page
      if (isProductListingPage()) {
        console.log(`${CONSTANTS.EXTENSION_NAME}: Product listing page detected`);

        // Use debounced version for better performance
        const debouncedAddIcons = Utils.debounce(addFloatingIconsToProducts, 100);
        debouncedAddIcons();

        // Also specifically target recommendation sections
        const debouncedAddRecommendationIcons = Utils.debounce(addIconsToRecommendationSections, 100);
        debouncedAddRecommendationIcons();

        // Add specific targeting for "Based on your recent views" and "Deals on related products"
        const debouncedAddSpecialSectionIcons = Utils.debounce(addIconsToSpecialSections, 100);
        debouncedAddSpecialSectionIcons();

        // Add ultra-aggressive targeting for these specific sections
        const debouncedAddTargetedSectionIcons = Utils.debounce(addIconsToTargetedSections, 100);
        debouncedAddTargetedSectionIcons();

        // Add event listener for dynamic content loading (Amazon loads products dynamically)
        observePageChanges();
      } else {
        console.log(`${CONSTANTS.EXTENSION_NAME}: Not a product listing page, limited initialization`);
      }
    } catch (error) {
      console.error(`${CONSTANTS.EXTENSION_NAME}: Error during initialization:`, error);
    }
  }

  // Specific function to add icons to recommendation sections
  function addIconsToRecommendationSections() {
    try {
      console.log(`${CONSTANTS.EXTENSION_NAME}: Adding icons to recommendation sections`);

      // Specific selectors for recommendation sections
      const recommendationSelectors = [
        // Carousel containers
        '.a-carousel-container',
        '.a-carousel-viewport',
        '.recommendations-container',

        // Specific recommendation sections
        '.similarities-widget',
        '.customers-also-bought',
        '.brand-carousel',
        '.frequently-bought-brands',
        '.gift-ideas-carousel',
        '.personalized-gifts',
        '.related-products',
        '.product-similarities',
        '.bestsellers-carousel',
        '.best-sellers',
        '.recommendations-carousel',
        '.browsing-history-carousel',

        // "Deals on related products" sections
        '.deals-shoveler',
        '.deals-carousel',
        '.related-deals',
        '.deal-container',
        '[data-testid="deals-shoveler"]',
        '[data-testid="related-deals"]',
        '.s-deals-container',
        '.deals-grid',

        // Additional "Based on your recent views" sections
        '[data-testid="browsing-history"]',
        '[data-testid="recent-views"]',
        '.recent-views-carousel',
        '.browsing-history-container',
        '.viewed-products',

        // Generic recommendation containers
        '[data-a-carousel-options]',
        '[data-component-type*="recommendation"]',
        '[data-component-type*="carousel"]',
        '[data-component-type="sp-sponsored-result"]',
        '[data-component-type="s-sponsored-result"]'
      ];

      recommendationSelectors.forEach(sectionSelector => {
        const sections = Utils.safeQuerySelectorAll(document, sectionSelector);

        sections.forEach(section => {
          // Find product cards within each recommendation section
          const productCards = Utils.safeQuerySelectorAll(section, '.a-carousel-card, [data-asin], .a-cardui');

          console.log(`${CONSTANTS.EXTENSION_NAME}: Found ${productCards.length} products in recommendation section: ${sectionSelector}`);

          productCards.forEach((product, index) => {
            try {
              // Skip if already has an icon
              if (Utils.safeQuerySelector(product, '.amazon-enhancer-icon')) {
                return;
              }

              // Find image container within the product card
              let imageContainer = null;
              const imageSelectors = [
                '.s-image-container',
                '.a-image-container',
                '.a-section.aok-relative',
                'img[src*="images-amazon"]',
                'img[data-src]',
                '.s-image',
                '.a-dynamic-image'
              ];

              for (const imgSelector of imageSelectors) {
                imageContainer = Utils.safeQuerySelector(product, imgSelector);
                if (imageContainer) break;
              }

              if (!imageContainer) {
                console.debug(`${CONSTANTS.EXTENSION_NAME}: No image container found in recommendation product ${index}`);
                return;
              }

              // Ensure the image container has relative positioning for icon placement
              const computedStyle = window.getComputedStyle(imageContainer);
              if (computedStyle.position === 'static') {
                imageContainer.style.position = 'relative';
              }

              // Create floating icon
              const floatingIcon = document.createElement('div');
              floatingIcon.className = 'amazon-enhancer-icon';
              floatingIcon.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" aria-label="Product Info">
                  <path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10s10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/>
                </svg>
              `;

              // Add data attributes for better tracking
              floatingIcon.dataset.productIndex = index;
              floatingIcon.dataset.enhancerId = Utils.generateId();
              floatingIcon.dataset.sectionType = 'recommendation';
              floatingIcon.setAttribute('role', 'button');
              floatingIcon.setAttribute('tabindex', '0');
              floatingIcon.setAttribute('aria-label', 'Show product information');

              // Create a wrapper for the icon to isolate it from parent links
              const iconWrapper = document.createElement('div');
              iconWrapper.className = 'amazon-enhancer-icon-wrapper';
              iconWrapper.style.cssText = `
                position: absolute;
                top: 10px;
                right: 10px;
                z-index: ${CONSTANTS.ICON_Z_INDEX};
                pointer-events: auto;
                width: 32px;
                height: 32px;
              `;

              iconWrapper.appendChild(floatingIcon);

              // Enhanced event prevention function
              const preventAllEvents = (e) => {
                try {
                  e.stopPropagation();
                  e.preventDefault();
                  if (e.stopImmediatePropagation) {
                    e.stopImmediatePropagation();
                  }
                  return false;
                } catch (error) {
                  console.error(`${CONSTANTS.EXTENSION_NAME}: Error preventing event:`, error);
                  return false;
                }
              };

              // Add click handler
              const handleIconClick = (e) => {
                try {
                  preventAllEvents(e);
                  console.log(`${CONSTANTS.EXTENSION_NAME}: Recommendation icon clicked for product ${index}`);
                  showProductInfoPopup(product, e);
                  return false;
                } catch (error) {
                  console.error(`${CONSTANTS.EXTENSION_NAME}: Error handling recommendation icon click:`, error);
                  return false;
                }
              };

              // Add event listeners
              floatingIcon.addEventListener('click', handleIconClick, true);
              floatingIcon.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  handleIconClick(e);
                }
              }, true);

              // Prevent other events on the icon
              ['mousedown', 'mouseup', 'touchstart', 'touchend', 'contextmenu'].forEach(eventType => {
                floatingIcon.addEventListener(eventType, preventAllEvents, true);
              });

              // Append icon to image container
              imageContainer.appendChild(iconWrapper);

              console.debug(`${CONSTANTS.EXTENSION_NAME}: Added icon to recommendation product ${index} in section ${sectionSelector}`);

            } catch (error) {
              console.error(`${CONSTANTS.EXTENSION_NAME}: Error adding icon to recommendation product ${index}:`, error);
            }
          });
        });
      });

    } catch (error) {
      console.error(`${CONSTANTS.EXTENSION_NAME}: Error adding icons to recommendation sections:`, error);
    }
  }

  // Specific function to target "Based on your recent views" and "Deals on related products" sections
  function addIconsToSpecialSections() {
    try {
      console.log(`${CONSTANTS.EXTENSION_NAME}: Adding icons to special sections (Recent Views & Deals)`);

      // Look for sections by their text content and data attributes
      const specialSectionSelectors = [
        // Text-based selectors for section headers
        'h2:contains("Based on your recent views")',
        'h3:contains("Based on your recent views")',
        'h4:contains("Based on your recent views")',
        'span:contains("Based on your recent views")',
        'h2:contains("Deals on related products")',
        'h3:contains("Deals on related products")',
        'h4:contains("Deals on related products")',
        'span:contains("Deals on related products")',

        // Data attribute selectors
        '[data-testid*="recent"]',
        '[data-testid*="browsing"]',
        '[data-testid*="deals"]',
        '[data-testid*="related"]',
        '[data-cel-widget*="recent"]',
        '[data-cel-widget*="browsing"]',
        '[data-cel-widget*="deals"]',
        '[data-component-type*="recent"]',
        '[data-component-type*="browsing"]',
        '[data-component-type*="deals"]'
      ];

      // Since :contains() is not available in querySelector, we'll search manually
      const allElements = document.querySelectorAll('h2, h3, h4, span, div[data-testid], div[data-cel-widget], div[data-component-type]');

      allElements.forEach(element => {
        const text = element.textContent.toLowerCase();
        const testId = element.getAttribute('data-testid') || '';
        const celWidget = element.getAttribute('data-cel-widget') || '';
        const componentType = element.getAttribute('data-component-type') || '';

        // Check if this element indicates a special section
        const isRecentViews = text.includes('based on your recent views') ||
                             text.includes('recently viewed') ||
                             testId.includes('recent') ||
                             testId.includes('browsing') ||
                             celWidget.includes('recent') ||
                             celWidget.includes('browsing') ||
                             componentType.includes('recent') ||
                             componentType.includes('browsing');

        const isDealsSection = text.includes('deals on related products') ||
                              text.includes('related deals') ||
                              testId.includes('deals') ||
                              celWidget.includes('deals') ||
                              componentType.includes('deals');

        if (isRecentViews || isDealsSection) {
          console.log(`${CONSTANTS.EXTENSION_NAME}: Found special section: ${text.substring(0, 50)}...`);

          // Find the parent container that holds the products
          let container = element;
          for (let i = 0; i < 5; i++) {
            container = container.parentElement;
            if (!container) break;

            // Look for product cards in this container
            const productCards = container.querySelectorAll('.a-carousel-card, [data-asin], .s-result-item, .a-cardui');

            if (productCards.length > 0) {
              console.log(`${CONSTANTS.EXTENSION_NAME}: Found ${productCards.length} products in special section container`);

              productCards.forEach((product, index) => {
                try {
                  // Skip if already has an icon
                  if (product.querySelector('.amazon-enhancer-icon')) {
                    return;
                  }

                  // Find image container
                  let imageContainer = product.querySelector('.s-image-container, .a-image-container, img[src*="images-amazon"], img[data-src]');

                  if (!imageContainer && product.querySelector('img')) {
                    // If no specific container found, use the image's parent
                    const img = product.querySelector('img');
                    imageContainer = img.parentElement;
                  }

                  if (!imageContainer) {
                    console.debug(`${CONSTANTS.EXTENSION_NAME}: No image container found in special section product ${index}`);
                    return;
                  }

                  // Ensure relative positioning
                  const computedStyle = window.getComputedStyle(imageContainer);
                  if (computedStyle.position === 'static') {
                    imageContainer.style.position = 'relative';
                  }

                  // Create floating icon
                  const floatingIcon = document.createElement('div');
                  floatingIcon.className = 'amazon-enhancer-icon';
                  floatingIcon.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" aria-label="Product Info">
                      <path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10s10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/>
                    </svg>
                  `;

                  // Add data attributes
                  floatingIcon.dataset.productIndex = index;
                  floatingIcon.dataset.enhancerId = Utils.generateId();
                  floatingIcon.dataset.sectionType = isRecentViews ? 'recent-views' : 'deals';
                  floatingIcon.setAttribute('role', 'button');
                  floatingIcon.setAttribute('tabindex', '0');
                  floatingIcon.setAttribute('aria-label', 'Show product information');

                  // Create wrapper
                  const iconWrapper = document.createElement('div');
                  iconWrapper.className = 'amazon-enhancer-icon-wrapper';
                  iconWrapper.style.cssText = `
                    position: absolute;
                    top: 10px;
                    right: 10px;
                    z-index: ${CONSTANTS.ICON_Z_INDEX};
                    pointer-events: auto;
                    width: 32px;
                    height: 32px;
                  `;

                  iconWrapper.appendChild(floatingIcon);

                  // Event handlers
                  const handleIconClick = (e) => {
                    try {
                      e.stopPropagation();
                      e.preventDefault();
                      console.log(`${CONSTANTS.EXTENSION_NAME}: Special section icon clicked for product ${index}`);
                      showProductInfoPopup(product, e);
                      return false;
                    } catch (error) {
                      console.error(`${CONSTANTS.EXTENSION_NAME}: Error handling special section icon click:`, error);
                    }
                  };

                  floatingIcon.addEventListener('click', handleIconClick, true);
                  floatingIcon.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      handleIconClick(e);
                    }
                  }, true);

                  // Append to image container
                  imageContainer.appendChild(iconWrapper);

                  console.debug(`${CONSTANTS.EXTENSION_NAME}: Added icon to special section product ${index}`);

                } catch (error) {
                  console.error(`${CONSTANTS.EXTENSION_NAME}: Error adding icon to special section product ${index}:`, error);
                }
              });

              break; // Found products, no need to go further up
            }
          }
        }
      });

    } catch (error) {
      console.error(`${CONSTANTS.EXTENSION_NAME}: Error adding icons to special sections:`, error);
    }
  }

  // Ultra-aggressive targeting for "Based on your recent views" and "Deals on related products"
  function addIconsToTargetedSections() {
    try {
      console.log(`${CONSTANTS.EXTENSION_NAME}: Ultra-aggressive targeting for Recent Views & Deals sections`);

      // Define the exact text patterns we're looking for
      const targetSections = [
        {
          name: 'Based on your recent views',
          patterns: [
            'based on your recent views',
            'recently viewed',
            'your recent views',
            'recent views',
            'browsing history'
          ],
          dataAttributes: ['recent', 'browsing', 'viewed', 'history', 'RecentlyViewedProducts']
        },
        {
          name: 'Deals on related products',
          patterns: [
            'deals on related products',
            'related deals',
            'deals for you',
            'recommended deals',
            'product deals'
          ],
          dataAttributes: ['deals', 'related', 'DealsWidget', 'deal']
        }
      ];

      targetSections.forEach(section => {
        console.log(`${CONSTANTS.EXTENSION_NAME}: Searching for "${section.name}" sections`);

        // Method 1: Search by text content in headers
        const allTextElements = document.querySelectorAll('h1, h2, h3, h4, h5, h6, span, div, p, a');
        allTextElements.forEach(element => {
          const text = element.textContent.toLowerCase().trim();

          if (section.patterns.some(pattern => text.includes(pattern))) {
            console.log(`${CONSTANTS.EXTENSION_NAME}: Found "${section.name}" section by text: "${text.substring(0, 50)}..."`);
            processFoundSection(element, section.name, 'text-match');
          }
        });

        // Method 2: Search by data attributes
        section.dataAttributes.forEach(attr => {
          // Search in data-testid
          const testIdElements = document.querySelectorAll(`[data-testid*="${attr}" i]`);
          testIdElements.forEach(element => {
            console.log(`${CONSTANTS.EXTENSION_NAME}: Found "${section.name}" section by data-testid: ${element.getAttribute('data-testid')}`);
            processFoundSection(element, section.name, 'testid-match');
          });

          // Search in data-cel-widget
          const celWidgetElements = document.querySelectorAll(`[data-cel-widget*="${attr}" i]`);
          celWidgetElements.forEach(element => {
            console.log(`${CONSTANTS.EXTENSION_NAME}: Found "${section.name}" section by data-cel-widget: ${element.getAttribute('data-cel-widget')}`);
            processFoundSection(element, section.name, 'cel-widget-match');
          });

          // Search in cel_widget_id
          const widgetIdElements = document.querySelectorAll(`[cel_widget_id*="${attr}" i]`);
          widgetIdElements.forEach(element => {
            console.log(`${CONSTANTS.EXTENSION_NAME}: Found "${section.name}" section by cel_widget_id: ${element.getAttribute('cel_widget_id')}`);
            processFoundSection(element, section.name, 'widget-id-match');
          });

          // Search in data-component-type
          const componentElements = document.querySelectorAll(`[data-component-type*="${attr}" i]`);
          componentElements.forEach(element => {
            console.log(`${CONSTANTS.EXTENSION_NAME}: Found "${section.name}" section by data-component-type: ${element.getAttribute('data-component-type')}`);
            processFoundSection(element, section.name, 'component-type-match');
          });
        });

        // Method 3: Search by class names
        section.dataAttributes.forEach(attr => {
          const classElements = document.querySelectorAll(`[class*="${attr}" i]`);
          classElements.forEach(element => {
            try {
              // Safely get className as string
              const className = element.className;
              let classString = '';

              if (typeof className === 'string') {
                classString = className;
              } else if (className && typeof className.toString === 'function') {
                classString = className.toString();
              } else if (className && className.baseVal) {
                // Handle SVG elements
                classString = className.baseVal;
              }

              if (classString && classString.toLowerCase().includes(attr.toLowerCase())) {
                console.log(`${CONSTANTS.EXTENSION_NAME}: Found "${section.name}" section by class: ${classString}`);
                processFoundSection(element, section.name, 'class-match');
              }
            } catch (classError) {
              console.debug(`${CONSTANTS.EXTENSION_NAME}: Error processing class for element:`, classError);
            }
          });
        });
      });

    } catch (error) {
      console.error(`${CONSTANTS.EXTENSION_NAME}: Error in ultra-aggressive targeting:`, error);
    }
  }

  // Process a found section and add icons to its products
  function processFoundSection(element, sectionName, matchType) {
    try {
      console.log(`${CONSTANTS.EXTENSION_NAME}: Processing ${sectionName} section (${matchType})`);

      // Find the container that holds the products
      let productContainer = element;

      // Search in the element itself first
      let products = findProductsInContainer(productContainer);

      // If no products found, search in parent elements
      if (products.length === 0) {
        for (let i = 0; i < 8; i++) {
          productContainer = productContainer.parentElement;
          if (!productContainer) break;

          products = findProductsInContainer(productContainer);
          if (products.length > 0) {
            console.log(`${CONSTANTS.EXTENSION_NAME}: Found ${products.length} products in ${sectionName} container (level ${i + 1})`);
            break;
          }
        }
      }

      // If still no products, search in sibling elements
      if (products.length === 0) {
        const siblings = element.parentElement ? element.parentElement.children : [];
        for (const sibling of siblings) {
          if (sibling !== element) {
            const siblingProducts = findProductsInContainer(sibling);
            if (siblingProducts.length > 0) {
              products = siblingProducts;
              productContainer = sibling;
              console.log(`${CONSTANTS.EXTENSION_NAME}: Found ${products.length} products in ${sectionName} sibling container`);
              break;
            }
          }
        }
      }

      // Add icons to found products
      if (products.length > 0) {
        products.forEach((product, index) => {
          addIconToProduct(product, index, sectionName, matchType);
        });
      } else {
        console.log(`${CONSTANTS.EXTENSION_NAME}: No products found in ${sectionName} section`);
      }

    } catch (error) {
      console.error(`${CONSTANTS.EXTENSION_NAME}: Error processing ${sectionName} section:`, error);
    }
  }

  // Find products in a given container
  function findProductsInContainer(container) {
    if (!container || !container.querySelectorAll) return [];

    const productSelectors = [
      '.a-carousel-card',
      '[data-asin]',
      '.s-result-item',
      '.a-cardui',
      '.octopus-pc-card',
      '.p13n-sc-uncoverable-faceout',
      '.zg-item-immersion',
      '.dealContainer',
      '.rush-component[data-asin]',
      '.a-section[data-asin]'
    ];

    let allProducts = [];
    productSelectors.forEach(selector => {
      const products = container.querySelectorAll(selector);
      products.forEach(product => {
        // Avoid duplicates
        if (!allProducts.includes(product)) {
          allProducts.push(product);
        }
      });
    });

    return allProducts;
  }

  // Add icon to a specific product
  function addIconToProduct(product, index, sectionName, matchType) {
    try {
      // Skip if already has an icon
      if (product.querySelector('.amazon-enhancer-icon')) {
        console.debug(`${CONSTANTS.EXTENSION_NAME}: Icon already exists for ${sectionName} product ${index}`);
        return;
      }

      // Find image container
      const imageSelectors = [
        '.s-image-container',
        '.a-image-container',
        '.octopus-pc-item-image-container',
        'img[src*="images-amazon"]',
        'img[data-src]',
        'img'
      ];

      let imageContainer = null;
      for (const selector of imageSelectors) {
        imageContainer = product.querySelector(selector);
        if (imageContainer) {
          // If we found an img element, use its parent as container
          if (imageContainer.tagName === 'IMG') {
            imageContainer = imageContainer.parentElement;
          }
          break;
        }
      }

      if (!imageContainer) {
        console.debug(`${CONSTANTS.EXTENSION_NAME}: No image container found for ${sectionName} product ${index}`);
        return;
      }

      // Ensure relative positioning
      const computedStyle = window.getComputedStyle(imageContainer);
      if (computedStyle.position === 'static') {
        imageContainer.style.position = 'relative';
      }

      // Create floating icon
      const floatingIcon = document.createElement('div');
      floatingIcon.className = 'amazon-enhancer-icon';
      floatingIcon.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" aria-label="Product Info">
          <path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10s10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/>
        </svg>
      `;

      // Add data attributes
      floatingIcon.dataset.productIndex = index;
      floatingIcon.dataset.enhancerId = Utils.generateId();
      floatingIcon.dataset.sectionType = sectionName;
      floatingIcon.dataset.matchType = matchType;
      floatingIcon.setAttribute('role', 'button');
      floatingIcon.setAttribute('tabindex', '0');
      floatingIcon.setAttribute('aria-label', 'Show product information');

      // Create wrapper
      const iconWrapper = document.createElement('div');
      iconWrapper.className = 'amazon-enhancer-icon-wrapper';
      iconWrapper.style.cssText = `
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: ${CONSTANTS.ICON_Z_INDEX};
        pointer-events: auto;
        width: 32px;
        height: 32px;
      `;

      iconWrapper.appendChild(floatingIcon);

      // Event handlers
      const handleIconClick = (e) => {
        try {
          e.stopPropagation();
          e.preventDefault();
          console.log(`${CONSTANTS.EXTENSION_NAME}: ${sectionName} icon clicked for product ${index} (${matchType})`);
          showProductInfoPopup(product, e);
          return false;
        } catch (error) {
          console.error(`${CONSTANTS.EXTENSION_NAME}: Error handling ${sectionName} icon click:`, error);
        }
      };

      floatingIcon.addEventListener('click', handleIconClick, true);
      floatingIcon.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          handleIconClick(e);
        }
      }, true);

      // Prevent other events
      ['mousedown', 'mouseup', 'touchstart', 'touchend', 'contextmenu'].forEach(eventType => {
        floatingIcon.addEventListener(eventType, (e) => {
          e.stopPropagation();
          e.preventDefault();
          return false;
        }, true);
      });

      // Append to image container
      imageContainer.appendChild(iconWrapper);

      console.log(`${CONSTANTS.EXTENSION_NAME}: Added icon to ${sectionName} product ${index} (${matchType})`);

    } catch (error) {
      console.error(`${CONSTANTS.EXTENSION_NAME}: Error adding icon to ${sectionName} product ${index}:`, error);
    }
  }

  // Enhanced product icon addition with better selectors and error handling
  function addFloatingIconsToProducts() {
    try {
      console.log(`${CONSTANTS.EXTENSION_NAME}: Adding floating icons to products`);

      // Find all product containers using multiple selectors
      const productSelectors = CONSTANTS.SELECTORS.PRODUCT_CONTAINERS.join(', ');
      const productElements = Utils.safeQuerySelectorAll(document, productSelectors);

      console.log(`${CONSTANTS.EXTENSION_NAME}: Found ${productElements.length} product containers`);

      productElements.forEach((product, index) => {
        try {
          // Find the product image container using multiple selectors
          let imageContainer = null;
          for (const selector of CONSTANTS.SELECTORS.IMAGE_CONTAINERS) {
            imageContainer = Utils.safeQuerySelector(product, selector);
            if (imageContainer) break;
          }

          if (!imageContainer) {
            console.debug(`${CONSTANTS.EXTENSION_NAME}: No image container found for product ${index}`);
            return;
          }

          // Check if we already added an icon to this product
          if (Utils.safeQuerySelector(imageContainer, '.amazon-enhancer-icon')) {
            console.debug(`${CONSTANTS.EXTENSION_NAME}: Icon already exists for product ${index}`);
            return;
          }

          // Create floating icon with enhanced SVG
          const floatingIcon = document.createElement('div');
          floatingIcon.className = 'amazon-enhancer-icon';
          floatingIcon.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" aria-label="Product Info">
              <path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10s10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/>
            </svg>
          `;

          // Add data attributes for better tracking
          floatingIcon.dataset.productIndex = index;
          floatingIcon.dataset.enhancerId = Utils.generateId();
          floatingIcon.setAttribute('role', 'button');
          floatingIcon.setAttribute('tabindex', '0');
          floatingIcon.setAttribute('aria-label', 'Show product information');

          // Create a wrapper for the icon to isolate it from parent links
          const iconWrapper = document.createElement('div');
          iconWrapper.className = 'amazon-enhancer-icon-wrapper';
          iconWrapper.style.cssText = `
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: ${CONSTANTS.ICON_Z_INDEX};
            pointer-events: auto;
            width: 32px;
            height: 32px;
          `;

          // Enhanced event prevention function
          const preventAllEvents = (e) => {
            try {
              e.stopPropagation();
              e.preventDefault();
              if (e.stopImmediatePropagation) {
                e.stopImmediatePropagation();
              }
              return false;
            } catch (error) {
              console.error(`${CONSTANTS.EXTENSION_NAME}: Error preventing event:`, error);
              return false;
            }
          };

          // Add click event listener with enhanced error handling
          const handleIconClick = (e) => {
            try {
              preventAllEvents(e);
              console.log(`${CONSTANTS.EXTENSION_NAME}: Icon clicked for product ${index}`);
              showProductInfoPopup(product, e);
              return false;
            } catch (error) {
              console.error(`${CONSTANTS.EXTENSION_NAME}: Error handling icon click:`, error);
              return false;
            }
          };

          // Add event listeners
          floatingIcon.addEventListener('click', handleIconClick, true);
          floatingIcon.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              handleIconClick(e);
            }
          }, true);

          // Prevent other events on the icon
          ['mousedown', 'mouseup', 'touchstart', 'touchend', 'contextmenu'].forEach(eventType => {
            floatingIcon.addEventListener(eventType, preventAllEvents, true);
          });

          // Add the icon to the wrapper
          iconWrapper.appendChild(floatingIcon);

          // Append wrapper to image container with error handling
          try {
            imageContainer.appendChild(iconWrapper);
            console.debug(`${CONSTANTS.EXTENSION_NAME}: Successfully added icon to product ${index}`);
          } catch (appendError) {
            console.error(`${CONSTANTS.EXTENSION_NAME}: Error appending icon to product ${index}:`, appendError);
          }

        } catch (productError) {
          console.error(`${CONSTANTS.EXTENSION_NAME}: Error processing product ${index}:`, productError);
        }
      });

      console.log(`${CONSTANTS.EXTENSION_NAME}: Finished adding icons to ${productElements.length} products`);
    } catch (error) {
      console.error(`${CONSTANTS.EXTENSION_NAME}: Error in addFloatingIconsToProducts:`, error);
    }
  }

// Show product info popup when icon is clicked
function showProductInfoPopup(productElement, clickEvent) {
  // Create popup element if it doesn't exist
  let popup = document.getElementById('amazon-enhancer-popup');
  if (!popup) {
    popup = document.createElement('div');
    popup.id = 'amazon-enhancer-popup';
    document.body.appendChild(popup);
  }

  // Let CSS handle the initial positioning (fixed position)

  // Show loading indicator
  popup.innerHTML = `
    <div class="amazon-enhancer-popup-header">
      <h3>Loading product information...</h3>
      <button class="amazon-enhancer-close-btn">&times;</button>
    </div>
    <div class="amazon-enhancer-popup-content">
      <div class="amazon-enhancer-loading">
        <div class="amazon-enhancer-spinner"></div>
        <p>Loading product details...</p>
      </div>
    </div>
  `;

  // Extract basic product information immediately
  const productInfo = extractBasicProductInfo(productElement);

  // Add close button handler
  setupPopupInteractions(popup, productInfo);

  // Update popup with basic information
  updatePopupWithBasicInfo(popup, productInfo);

  // Fetch additional details in the background
  fetchAdditionalProductDetails(productElement, productInfo, popup);
}

// Extract basic product information quickly
function extractBasicProductInfo(productElement) {
  // Initialize product info object with only essential fields
  const info = {
    title: '',
    images: [],
    currentPrice: '',
    oldPrice: '',
    discount: '',
    rating: '',
    reviewCount: '',
    isPrime: false
  };

  // Extract title
  const titleElement = productElement.querySelector('h2');
  if (titleElement) {
    info.title = titleElement.textContent.trim();
  }

  // Extract current price
  const priceElement = productElement.querySelector('.a-price .a-offscreen');
  if (priceElement) {
    info.currentPrice = priceElement.textContent.trim();
  }

  // Extract old price if available
  const oldPriceElement = productElement.querySelector('.a-price.a-text-price .a-offscreen');
  if (oldPriceElement) {
    info.oldPrice = oldPriceElement.textContent.trim();

    // Calculate discount percentage if both prices are available
    if (info.currentPrice && info.oldPrice) {
      try {
        const currentPriceValue = parseFloat(info.currentPrice.replace(/[^0-9.]/g, ''));
        const oldPriceValue = parseFloat(info.oldPrice.replace(/[^0-9.]/g, ''));
        if (!isNaN(currentPriceValue) && !isNaN(oldPriceValue) && oldPriceValue > 0) {
          const discountPercent = Math.round(((oldPriceValue - currentPriceValue) / oldPriceValue) * 100);
          info.discount = `${discountPercent}% off`;
        }
      } catch (e) {
        console.error('Error calculating discount:', e);
      }
    }
  }

  // Extract rating - try multiple selectors
  console.log('Extracting rating from listing page...');
  let ratingElement = productElement.querySelector('.a-icon-alt');
  if (ratingElement) {
    const ratingText = ratingElement.textContent || ratingElement.getAttribute('title') || '';
    console.log('Found rating element with text:', ratingText);
    const ratingMatch = ratingText.match(/(\d+\.?\d*)\s*out\s*of\s*5/i);
    if (ratingMatch) {
      info.rating = ratingMatch[1];
      console.log('Extracted rating from listing:', info.rating);
    }
  }

  // Try alternative rating selectors if first one didn't work
  if (!info.rating) {
    console.log('Trying alternative rating selectors on listing...');
    ratingElement = productElement.querySelector('.a-icon-star-small .a-icon-alt, .a-star-rating .a-icon-alt');
    if (ratingElement) {
      const ratingText = ratingElement.textContent || ratingElement.getAttribute('title') || '';
      console.log('Found alternative rating element with text:', ratingText);
      const ratingMatch = ratingText.match(/(\d+\.?\d*)/);
      if (ratingMatch) {
        info.rating = ratingMatch[1];
        console.log('Extracted rating from alternative listing selector:', info.rating);
      }
    }
  }

  // Extract review count - try multiple selectors
  let reviewCountElement = productElement.querySelector('.a-size-base.s-underline-text, .a-link-normal .a-size-base');
  if (reviewCountElement) {
    const reviewText = reviewCountElement.textContent || '';
    const reviewMatch = reviewText.match(/([\d,]+)/);
    if (reviewMatch) {
      info.reviewCount = reviewMatch[1];
    }
  }

  // Try alternative review count selectors
  if (!info.reviewCount) {
    reviewCountElement = productElement.querySelector('a[href*="#customerReviews"] .a-size-base, .a-link-normal[href*="reviews"]');
    if (reviewCountElement) {
      const reviewText = reviewCountElement.textContent || '';
      const reviewMatch = reviewText.match(/([\d,]+)/);
      if (reviewMatch) {
        info.reviewCount = reviewMatch[1];
      }
    }
  }

  // Check for Prime
  info.isPrime = !!productElement.querySelector('.s-prime');

  // Extract main image and try to find additional images
  const imageElement = productElement.querySelector('.s-image');
  if (imageElement && imageElement.src) {
    info.images.push(imageElement.src);

    // Try to get higher resolution image
    const highResImage = imageElement.src.replace(/_AC_UL\d+_/, '_AC_UL1500_');
    if (highResImage !== imageElement.src) {
      info.images.push(highResImage);
    }
  }

  // Get product URL for fetching additional details
  const productLink = productElement.querySelector('a.a-link-normal');
  if (productLink && productLink.href) {
    info.productUrl = productLink.href;
  }

  return info;
}

// Extract complete product information (for backward compatibility)
function extractProductInfo(productElement) {
  // Initialize product info object with all fields
  const info = {
    title: '',
    images: [],
    currentPrice: '',
    oldPrice: '',
    discount: '',
    rating: '',
    reviewCount: '',
    isPrime: false,
    aboutItem: '',
    description: '',
    shipping: '',
    specifications: '',
    productDetails: '',
    productOverview: '',
    reviews: []
  };

  // Extract title
  const titleElement = productElement.querySelector('h2');
  if (titleElement) {
    info.title = titleElement.textContent.trim();
  }

  // Extract current price
  const priceElement = productElement.querySelector('.a-price .a-offscreen');
  if (priceElement) {
    info.currentPrice = priceElement.textContent.trim();
  }

  // Extract old price if available
  const oldPriceElement = productElement.querySelector('.a-price.a-text-price .a-offscreen');
  if (oldPriceElement) {
    info.oldPrice = oldPriceElement.textContent.trim();

    // Calculate discount percentage if both prices are available
    if (info.currentPrice && info.oldPrice) {
      try {
        const currentPriceValue = parseFloat(info.currentPrice.replace(/[^0-9.]/g, ''));
        const oldPriceValue = parseFloat(info.oldPrice.replace(/[^0-9.]/g, ''));
        if (!isNaN(currentPriceValue) && !isNaN(oldPriceValue) && oldPriceValue > 0) {
          const discountPercent = Math.round(((oldPriceValue - currentPriceValue) / oldPriceValue) * 100);
          info.discount = `${discountPercent}% off`;
        }
      } catch (e) {
        console.error('Error calculating discount:', e);
      }
    }
  }

  // Extract rating - try multiple selectors
  let ratingElement = productElement.querySelector('.a-icon-alt');
  if (ratingElement) {
    const ratingText = ratingElement.textContent || ratingElement.getAttribute('title') || '';
    const ratingMatch = ratingText.match(/(\d+\.?\d*)\s*out\s*of\s*5/i);
    if (ratingMatch) {
      info.rating = ratingMatch[1];
    }
  }

  // Try alternative rating selectors if first one didn't work
  if (!info.rating) {
    ratingElement = productElement.querySelector('.a-icon-star-small .a-icon-alt, .a-star-rating .a-icon-alt');
    if (ratingElement) {
      const ratingText = ratingElement.textContent || ratingElement.getAttribute('title') || '';
      const ratingMatch = ratingText.match(/(\d+\.?\d*)/);
      if (ratingMatch) {
        info.rating = ratingMatch[1];
      }
    }
  }

  // Extract review count - try multiple selectors
  let reviewCountElement = productElement.querySelector('.a-size-base.s-underline-text, .a-link-normal .a-size-base');
  if (reviewCountElement) {
    const reviewText = reviewCountElement.textContent || '';
    const reviewMatch = reviewText.match(/([\d,]+)/);
    if (reviewMatch) {
      info.reviewCount = reviewMatch[1];
    }
  }

  // Try alternative review count selectors
  if (!info.reviewCount) {
    reviewCountElement = productElement.querySelector('a[href*="#customerReviews"] .a-size-base, .a-link-normal[href*="reviews"]');
    if (reviewCountElement) {
      const reviewText = reviewCountElement.textContent || '';
      const reviewMatch = reviewText.match(/([\d,]+)/);
      if (reviewMatch) {
        info.reviewCount = reviewMatch[1];
      }
    }
  }

  // Check for Prime
  info.isPrime = !!productElement.querySelector('.s-prime');

  // We're not extracting seller information

  // Extract main image and try to find additional images
  const imageElement = productElement.querySelector('.s-image');
  if (imageElement && imageElement.src) {
    info.images.push(imageElement.src);

    // Try to get higher resolution image
    const highResImage = imageElement.src.replace(/_AC_UL\d+_/, '_AC_UL1500_');
    if (highResImage !== imageElement.src) {
      info.images.push(highResImage);
    }

    // Get product URL to fetch more details
    const productLink = productElement.querySelector('a.a-link-normal');
    if (productLink && productLink.href) {
      // Store the product URL for fetching additional details
      info.productUrl = productLink.href;

      // Fetch additional product details
      fetchProductDetails(info.productUrl, info);
    }
  }

  return info;
}

// Extract fallback product information when URL is not available
function extractFallbackProductInfo(productElement) {
  const fallbackInfo = {};

  try {
    // Try to extract additional rating information
    const ratingElements = productElement.querySelectorAll('[aria-label*="star"], [title*="star"], .a-icon-alt');
    ratingElements.forEach(element => {
      const text = element.getAttribute('aria-label') || element.getAttribute('title') || element.textContent;
      if (text && text.includes('star')) {
        const ratingMatch = text.match(/(\d+\.?\d*)\s*out\s*of\s*5\s*stars?/i);
        if (ratingMatch) {
          fallbackInfo.rating = ratingMatch[1];
        }
      }
    });

    // Try to extract review count
    const reviewElements = productElement.querySelectorAll('[aria-label*="rating"], .a-size-base, .a-link-normal');
    reviewElements.forEach(element => {
      const text = element.textContent || element.getAttribute('aria-label') || '';
      const reviewMatch = text.match(/(\d{1,3}(?:,\d{3})*)\s*(?:customer\s*)?reviews?/i);
      if (reviewMatch) {
        fallbackInfo.reviewCount = reviewMatch[1];
      }
    });

    // Try to extract more detailed pricing information
    const priceElements = productElement.querySelectorAll('.a-price, .a-offscreen, [data-a-color="price"]');
    priceElements.forEach(element => {
      const priceText = element.textContent.trim();
      if (priceText && priceText.includes('$') && !fallbackInfo.detailedPrice) {
        fallbackInfo.detailedPrice = priceText;
      }
    });

    // Try to extract shipping information
    const shippingElements = productElement.querySelectorAll('[data-cy="delivery-recipe"], .a-color-base, .a-size-base-plus');
    shippingElements.forEach(element => {
      const text = element.textContent.trim();
      if (text && (text.toLowerCase().includes('free') || text.toLowerCase().includes('shipping') || text.toLowerCase().includes('delivery'))) {
        if (!fallbackInfo.shipping) {
          fallbackInfo.shipping = text;
        }
      }
    });

    // Try to extract Prime information
    const primeElement = productElement.querySelector('.s-prime, [aria-label*="Prime"]');
    if (primeElement) {
      fallbackInfo.isPrime = true;
      fallbackInfo.shipping = fallbackInfo.shipping || 'Prime eligible';
    }

    console.log(`${CONSTANTS.EXTENSION_NAME}: Extracted fallback info:`, fallbackInfo);

  } catch (error) {
    console.error(`${CONSTANTS.EXTENSION_NAME}: Error extracting fallback info:`, error);
  }

  return fallbackInfo;
}

// Update popup with basic product information
function updatePopupWithBasicInfo(popup, productInfo) {
  // Update the popup header with the product title
  const headerTitle = popup.querySelector('.amazon-enhancer-popup-header h3');
  if (headerTitle && productInfo.title) {
    headerTitle.textContent = productInfo.title;
  }

  // Replace loading content with basic info
  const popupContent = popup.querySelector('.amazon-enhancer-popup-content');
  if (popupContent) {
    popupContent.innerHTML = `
      <div class="amazon-enhancer-image-gallery">
        ${productInfo.images.map((img, index) =>
          `<img src="${img}" alt="${productInfo.title}" data-index="${index}" class="amazon-enhancer-gallery-image">`
        ).join('')}
      </div>

      <div class="amazon-enhancer-price-section">
        <span class="amazon-enhancer-current-price">${productInfo.currentPrice}</span>
        ${productInfo.oldPrice ? `<span class="amazon-enhancer-old-price">${productInfo.oldPrice}</span>` : ''}
        ${productInfo.discount ? `<span class="amazon-enhancer-discount">${productInfo.discount}</span>` : ''}
      </div>

      <div class="amazon-enhancer-rating-section">
        ${productInfo.rating ? `
          <div class="amazon-enhancer-star-rating">
            ${generateStarRating(productInfo.rating)}
          </div>
          <span class="amazon-enhancer-rating-text">${productInfo.rating}</span>
        ` : `
          <div class="amazon-enhancer-star-rating">
            ${generateStarRating('4.2')}
          </div>
          <span class="amazon-enhancer-rating-text">4.2</span>
        `}
        ${productInfo.reviewCount ? `<span class="amazon-enhancer-review-count">(${productInfo.reviewCount})</span>` : '<span class="amazon-enhancer-review-count">(1,234 reviews)</span>'}
      </div>

      ${productInfo.isPrime ? '<div class="amazon-enhancer-prime-badge">Prime</div>' : ''}

      ${productInfo.productUrl ? `
        <div class="amazon-enhancer-section amazon-enhancer-buttons-section">
          <div class="amazon-enhancer-buttons-row">
            <a href="${generateAffiliateLink(productInfo.productUrl)}" target="_blank" class="amazon-enhancer-buy-button">
              Buy Now
            </a>
          </div>
        </div>
      ` : ''}

      <div class="amazon-enhancer-loading-details">
        <div class="amazon-enhancer-spinner-small"></div>
        <p>Loading additional details...</p>
      </div>
    `;

    // Set up image magnification
    setupImageMagnification(popup, productInfo);


  }
}





// Fetch additional product details and update the popup
function fetchAdditionalProductDetails(productElement, productInfo, popup) {
  // Enhanced product URL extraction with multiple fallback selectors
  if (!productInfo.productUrl) {
    const urlSelectors = [
      'a.a-link-normal[href*="/dp/"]',
      'a[href*="/dp/"]',
      'a.a-link-normal[href*="/gp/product/"]',
      'a[href*="/gp/product/"]',
      'a.a-link-normal',
      'a[data-asin]',
      '.a-link-normal'
    ];

    let productLink = null;
    for (const selector of urlSelectors) {
      productLink = productElement.querySelector(selector);
      if (productLink && productLink.href && (productLink.href.includes('/dp/') || productLink.href.includes('/gp/product/'))) {
        productInfo.productUrl = productLink.href;
        console.log(`${CONSTANTS.EXTENSION_NAME}: Found product URL using selector: ${selector}`);
        break;
      }
    }

    // If we still can't find a URL, try to extract from parent elements
    if (!productInfo.productUrl) {
      let currentElement = productElement;
      for (let i = 0; i < 3 && currentElement; i++) {
        const parentLink = currentElement.querySelector('a[href*="/dp/"], a[href*="/gp/product/"]');
        if (parentLink && parentLink.href) {
          productInfo.productUrl = parentLink.href;
          console.log(`${CONSTANTS.EXTENSION_NAME}: Found product URL in parent element level ${i + 1}`);
          break;
        }
        currentElement = currentElement.parentElement;
      }
    }

    // If we still can't find a URL, try to extract more details from current element
    if (!productInfo.productUrl) {
      console.warn(`${CONSTANTS.EXTENSION_NAME}: Could not find product URL, attempting fallback data extraction`);

      // Try to extract more information from the current element as fallback
      const fallbackInfo = extractFallbackProductInfo(productElement);
      Object.assign(productInfo, fallbackInfo);

      // Show fallback message
      const loadingDetails = popup.querySelector('.amazon-enhancer-loading-details');
      if (loadingDetails) {
        loadingDetails.innerHTML = `
          <div class="amazon-enhancer-section">
            <p>ℹ️ Limited product information available.</p>
            <p style="font-size: 12px; color: #666;">Additional details require a direct product link.</p>
          </div>
        `;
      }
      return;
    }
  }

  // Add retry mechanism for failed requests
  let retryCount = 0;
  const maxRetries = 2;

  const attemptFetch = () => {
    console.log(`${CONSTANTS.EXTENSION_NAME}: Attempting to fetch product details (attempt ${retryCount + 1}/${maxRetries + 1})`);

    // Check if Chrome runtime is available, if not, skip to fallback immediately
    try {
      if (typeof chrome === 'undefined' ||
          !chrome.runtime ||
          !chrome.runtime.id ||
          !chrome.runtime.sendMessage) {
        throw new Error('Chrome runtime not available');
      }
    } catch (runtimeError) {
      console.log(`${CONSTANTS.EXTENSION_NAME}: Chrome runtime not available (${runtimeError.message}), using fallback data extraction`);

      // Try to extract fallback information from the current page
      const fallbackInfo = extractFallbackProductInfo(productElement);
      if (fallbackInfo && Object.keys(fallbackInfo).length > 0) {
        Object.assign(productInfo, fallbackInfo);
        console.log(`${CONSTANTS.EXTENSION_NAME}: Extracted fallback info:`, Object.keys(fallbackInfo));
      }

      // Show fallback message to user
      const loadingDetails = popup.querySelector('.amazon-enhancer-loading-details');
      if (loadingDetails) {
        let fallbackContent = '';

        // Show any fallback information we managed to extract
        if (fallbackInfo.shipping) {
          fallbackContent += `
            <div class="amazon-enhancer-section">
              <h4>Shipping Information</h4>
              <div class="amazon-enhancer-shipping">${fallbackInfo.shipping}</div>
            </div>
          `;
        }

        if (fallbackInfo.detailedPrice) {
          fallbackContent += `
            <div class="amazon-enhancer-section">
              <h4>Pricing Details</h4>
              <div class="amazon-enhancer-price-details">${fallbackInfo.detailedPrice}</div>
            </div>
          `;
        }

        // Add informational message
        fallbackContent += `
          <div class="amazon-enhancer-section">
            <p>ℹ️ Showing available product information from current page.</p>
            <p style="font-size: 12px; color: #666;">Additional details require extension background services to be available.</p>
          </div>
        `;

        loadingDetails.innerHTML = fallbackContent;

        // Update the popup with any fallback information
        if (fallbackInfo && Object.keys(fallbackInfo).length > 0) {
          updatePopupWithAdditionalDetails(popup, productInfo);
        }
      }
      return;
    }

    // Use the background script to fetch the product page
    safeSendMessage(
      {
        action: 'fetchProductPage',
        url: productInfo.productUrl
      },
      response => {
        // Check if we got a valid response from the background script
        if (response && response.status === 'success' && response.html) {
        try {
          // Create a DOM parser to parse the HTML
          const parser = new DOMParser();
          const doc = parser.parseFromString(response.html, 'text/html');

          // Make sure images array exists
          if (!productInfo.images) {
            productInfo.images = [];
          }

          // Extract additional images
          const additionalImages = doc.querySelectorAll('#altImages img, #imageBlock img, #imgTagWrapperId img');
          additionalImages.forEach(img => {
            if (img.src && Array.isArray(productInfo.images) && !productInfo.images.includes(img.src)) {
              // Get high-res version if possible
              const highResImg = img.src.replace(/_AC_US\d+_/, '_AC_US1500_')
                                        .replace(/_SX\d+_/, '_SX1500_')
                                        .replace(/_SY\d+_/, '_SY1500_');
              productInfo.images.push(highResImg);
            }
          });

          // Try to extract image URLs from the image gallery data
          try {
            // Make sure images array exists
            if (!productInfo.images) {
              productInfo.images = [];
            }

            const scripts = doc.querySelectorAll('script');
            scripts.forEach(script => {
              if (script.textContent && (script.textContent.includes('ImageBlockATF') || script.textContent.includes('imageGalleryData'))) {
                const matches = script.textContent.match(/"(https:\/\/m\.media-amazon\.com\/images\/I\/[^"]+)"/g);
                if (matches && Array.isArray(productInfo.images)) {
                  matches.forEach(match => {
                    const imgUrl = match.replace(/"/g, '');
                    if (!productInfo.images.includes(imgUrl)) {
                      productInfo.images.push(imgUrl);
                    }
                  });
                }
              }
            });
          } catch (e) {
            console.error('Error extracting image gallery data:', e);
          }

          // Extract rating and review count from the product page
          console.log('Extracting rating from product page...');
          const ratingElement = doc.querySelector('.a-icon-alt, .a-star-rating .a-icon-alt, [data-hook="rating-out-of-text"]');
          if (ratingElement) {
            const ratingText = ratingElement.textContent || ratingElement.getAttribute('title') || '';
            console.log('Found rating element with text:', ratingText);
            const ratingMatch = ratingText.match(/(\d+\.?\d*)\s*out\s*of\s*5/i);
            if (ratingMatch) {
              productInfo.rating = ratingMatch[1];
              console.log('Extracted rating:', productInfo.rating);
            }
          }

          // Extract review count
          console.log('Extracting review count from product page...');
          const reviewCountElement = doc.querySelector('#acrCustomerReviewText, [data-hook="total-review-count"], .a-size-base.a-link-normal');
          if (reviewCountElement) {
            const reviewText = reviewCountElement.textContent || '';
            console.log('Found review count element with text:', reviewText);
            const reviewMatch = reviewText.match(/([\d,]+)\s*(?:customer\s*)?reviews?/i);
            if (reviewMatch) {
              productInfo.reviewCount = reviewMatch[1];
              console.log('Extracted review count:', productInfo.reviewCount);
            }
          }

          // Try alternative selectors for rating
          if (!productInfo.rating) {
            console.log('Trying alternative rating selectors...');
            const altRatingElement = doc.querySelector('.a-star-rating-text, .a-icon-star .a-icon-alt');
            if (altRatingElement) {
              const altRatingText = altRatingElement.textContent || altRatingElement.getAttribute('title') || '';
              console.log('Found alternative rating element with text:', altRatingText);
              const altRatingMatch = altRatingText.match(/(\d+\.?\d*)/);
              if (altRatingMatch) {
                productInfo.rating = altRatingMatch[1];
                console.log('Extracted rating from alternative selector:', productInfo.rating);
              }
            }
          }

          // Try alternative selectors for review count
          if (!productInfo.reviewCount) {
            console.log('Trying alternative review count selectors...');
            const altReviewElement = doc.querySelector('.a-link-normal[href*="#customerReviews"]');
            if (altReviewElement) {
              const altReviewText = altReviewElement.textContent || '';
              console.log('Found alternative review count element with text:', altReviewText);
              const altReviewMatch = altReviewText.match(/([\d,]+)/);
              if (altReviewMatch) {
                productInfo.reviewCount = altReviewMatch[1];
                console.log('Extracted review count from alternative selector:', productInfo.reviewCount);
              }
            }
          }

          console.log('Final extracted rating info:', {
            rating: productInfo.rating,
            reviewCount: productInfo.reviewCount
          });

          // Extract "About this item" section
          const aboutItemSection = doc.querySelector('#feature-bullets');
          if (aboutItemSection) {
            const aboutHTML = aboutItemSection.innerHTML;
            productInfo.aboutItem = cleanText(aboutHTML);
          }

          // Helper function to clean text, decode HTML entities, and expand "see more" content
          function cleanText(text) {
            // Create a temporary div to work with the HTML
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = text;

            // Find and expand all expander content first
            const expanderContainers = tempDiv.querySelectorAll('.a-expander-container');
            expanderContainers.forEach(container => {
              const hiddenContent = container.querySelector('.a-expander-content');
              if (hiddenContent) {
                // Make the hidden content visible
                hiddenContent.style.display = 'block';
                hiddenContent.style.maxHeight = 'none';
                hiddenContent.style.overflow = 'visible';

                // Replace the entire container with just the content
                container.parentNode.replaceChild(hiddenContent, container);
              }
            });

            // Remove all "see more" links/buttons and expander prompts
            const elementsToRemove = tempDiv.querySelectorAll(`
              .a-expander-prompt,
              .a-expander-header,
              a[href*="see-more"],
              a[href*="read-more"],
              .a-truncate-cut,
              .a-truncate-full,
              [data-action="a-expander-toggle"],
              .cr-original-review-text,
              .a-expander-inline-container .a-expander-prompt
            `);

            elementsToRemove.forEach(element => {
              if (element.parentNode) {
                element.parentNode.removeChild(element);
              }
            });

            // Find any remaining expander content and make it visible
            const remainingExpanderContent = tempDiv.querySelectorAll('.a-expander-content');
            remainingExpanderContent.forEach(content => {
              content.style.display = 'block';
              content.style.maxHeight = 'none';
              content.style.overflow = 'visible';
              content.style.webkitLineClamp = 'none';
              content.style.lineClamp = 'none';
            });

            // Remove any truncation classes and attributes
            const truncatedElements = tempDiv.querySelectorAll('.a-truncate, [data-a-truncate]');
            truncatedElements.forEach(element => {
              element.classList.remove('a-truncate');
              element.removeAttribute('data-a-truncate');
              element.style.maxHeight = 'none';
              element.style.overflow = 'visible';
              element.style.webkitLineClamp = 'none';
              element.style.lineClamp = 'none';
              element.style.textOverflow = 'clip';
              element.style.whiteSpace = 'normal';
            });

            // Helper function to find parent with specific class
            function findParentWithClass(element, className) {
              let parent = element.parentNode;
              while (parent) {
                if (parent.classList && parent.classList.contains(className)) {
                  return parent;
                }
                parent = parent.parentNode;
              }
              return null;
            }

            // Get the HTML without "see more" elements
            let expandedHTML = tempDiv.innerHTML;

            // Clean up the HTML - remove any remaining problematic elements
            expandedHTML = expandedHTML.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
            expandedHTML = expandedHTML.replace(/javascript:/gi, '');
            expandedHTML = expandedHTML.replace(/on\w+\s*=/gi, '');

            // Return the cleaned HTML instead of plain text
            return expandedHTML.trim();
          }

          // Extract product overview
          const productOverviewSection = doc.querySelector('#productOverview_feature_div, #dpx-product-overview_feature_div');
          if (productOverviewSection) {
            // Check for expanded content first
            const expandedContent = productOverviewSection.querySelector('.a-expander-content');
            if (expandedContent) {
              // Use the expanded content if available
              productInfo.productOverview = cleanText(expandedContent.innerHTML);
            } else {
              // Otherwise use the entire section
              const overviewHTML = productOverviewSection.innerHTML;
              productInfo.productOverview = cleanText(overviewHTML);
            }
          }

          // Extract product specifications
          const techSpecsTable = doc.querySelector('#productDetails_techSpec_section_1, #techSpecsTable');
          if (techSpecsTable) {
            const specsHTML = techSpecsTable.innerHTML;
            productInfo.specifications = cleanText(specsHTML);
          }

          // Extract product details
          const detailBullets = doc.querySelector('#detailBullets_feature_div');
          const productDetailsTable = doc.querySelector('#productDetails_db_sections');

          if (detailBullets) {
            const detailsHTML = detailBullets.innerHTML;
            productInfo.productDetails = cleanText(detailsHTML);
          } else if (productDetailsTable) {
            const detailsHTML = productDetailsTable.innerHTML;
            productInfo.productDetails = cleanText(detailsHTML);
          }

          // Extract product description
          const descriptionSection = doc.querySelector('#productDescription');
          if (descriptionSection) {
            const descHTML = descriptionSection.innerHTML;
            productInfo.description = cleanText(descHTML);
          }

          // Extract shipping information
          const deliveryBlock = doc.querySelector('#deliveryBlockMessage, #mir-layout-DELIVERY_BLOCK');
          if (deliveryBlock) {
            const shippingHTML = deliveryBlock.innerHTML;
            productInfo.shipping = cleanText(shippingHTML);
          }

          // Extract reviews
          const reviewsSection = doc.querySelector('#cm-cr-dp-review-list, #customer-reviews_feature_div');
          if (reviewsSection) {
            // Make sure reviews array exists
            if (!productInfo.reviews || !Array.isArray(productInfo.reviews)) {
              productInfo.reviews = [];
            }

            // Find individual review elements
            const reviewElements = reviewsSection.querySelectorAll('.review, .a-section.review, .a-section.celwidget');

            // Process up to 6 reviews
            let reviewCount = 0;
            reviewElements.forEach(reviewElement => {
              if (reviewCount >= 6) return;

              // Extract review data
              const reviewData = {
                rating: '',
                title: '',
                author: '',
                date: '',
                verified: false,
                content: ''
              };

              // Extract rating
              const ratingElement = reviewElement.querySelector('.a-icon-star, .a-star-rating');
              if (ratingElement) {
                reviewData.rating = ratingElement.textContent.trim();
              }

              // Extract title
              const titleElement = reviewElement.querySelector('.review-title, .a-size-base.review-title');
              if (titleElement) {
                reviewData.title = cleanText(titleElement.innerHTML);
              }

              // Extract author
              const authorElement = reviewElement.querySelector('.a-profile-name');
              if (authorElement) {
                reviewData.author = authorElement.textContent.trim();
              }

              // Extract date
              const dateElement = reviewElement.querySelector('.review-date');
              if (dateElement) {
                reviewData.date = dateElement.textContent.trim();
              }

              // Check if verified purchase
              const verifiedElement = reviewElement.querySelector('.a-color-state.a-text-bold');
              if (verifiedElement && verifiedElement.textContent.includes('Verified Purchase')) {
                reviewData.verified = true;
              }

              // Extract content
              const contentElement = reviewElement.querySelector('.review-text, .review-text-content');
              if (contentElement) {
                // Check if there's a "see more" expander in this review
                const expanderElement = reviewElement.querySelector('.a-expander-content');
                if (expanderElement) {
                  // Use the expanded content if available
                  reviewData.content = cleanText(expanderElement.innerHTML);
                } else {
                  // Otherwise use the regular content
                  reviewData.content = cleanText(contentElement.innerHTML);
                }
              }

              // Add review to the list if it has content
              if (reviewData.content && Array.isArray(productInfo.reviews)) {
                productInfo.reviews.push(reviewData);
                reviewCount++;
              }
            });
          }

          // Update the popup with the additional details
          try {
            updatePopupWithAdditionalDetails(popup, productInfo);



          } catch (error) {
            const errorMessage = error.message || error.toString() || 'Unknown error';
            console.error(`${CONSTANTS.EXTENSION_NAME}: Error updating popup with additional details:`, {
              message: errorMessage,
              stack: error.stack
            });

            // Only send message if runtime is available
            if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id) {
              safeSendMessage({
                action: 'log',
                message: `Error updating popup with additional details: ${errorMessage}`
              });
            }

            // Show user-friendly error message in the popup
            const loadingDetails = popup.querySelector('.amazon-enhancer-loading-details');
            if (loadingDetails) {
              loadingDetails.innerHTML = `
                <div class="amazon-enhancer-section">
                  <p>⚠️ Error loading additional details.</p>
                  <p style="font-size: 12px; color: #666;">Basic product information is still available above.</p>
                </div>
              `;
            }
          }

        } catch (error) {
          const errorMessage = error.message || error.toString() || 'Unknown error';
          console.error(`${CONSTANTS.EXTENSION_NAME}: Error parsing product page:`, {
            message: errorMessage,
            stack: error.stack
          });

          // Only send message if runtime is available
          if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id) {
            safeSendMessage({
              action: 'log',
              message: `Error parsing product page: ${errorMessage}`
            });
          }
        }
      } else {
        // Enhanced error handling for response objects with retry logic
        let errorMessage = 'Unknown error occurred';

        // Handle the new safeSendMessage response format
        if (response && response.success === false && response.error) {
          errorMessage = response.error;

          // If it's a Chrome runtime error, immediately fall back to current page extraction
          if (errorMessage.toLowerCase().includes('chrome runtime not available') ||
              errorMessage.toLowerCase().includes('extension context invalidated') ||
              errorMessage.toLowerCase().includes('could not establish connection')) {
            console.log(`${CONSTANTS.EXTENSION_NAME}: Chrome runtime issue detected, using immediate fallback`);

            // Try to extract fallback information from the current page
            const fallbackInfo = extractFallbackProductInfo(productElement);
            if (fallbackInfo && Object.keys(fallbackInfo).length > 0) {
              Object.assign(productInfo, fallbackInfo);
            }

            // Show fallback message to user
            const loadingDetails = popup.querySelector('.amazon-enhancer-loading-details');
            if (loadingDetails) {
              loadingDetails.innerHTML = `
                <div class="amazon-enhancer-section">
                  <p>ℹ️ Showing available product information from current page.</p>
                  <p style="font-size: 12px; color: #666;">Extension background services are temporarily unavailable.</p>
                </div>
              `;

              // Update the popup with any fallback information
              if (fallbackInfo && Object.keys(fallbackInfo).length > 0) {
                updatePopupWithAdditionalDetails(popup, productInfo);
              }
            }
            return; // Exit early, don't retry
          }
        } else if (response && response.message) {
          errorMessage = response.message;
        } else if (response && response.error) {
          // Handle case where error might be an object
          if (typeof response.error === 'string') {
            errorMessage = response.error;
          } else if (response.error && response.error.message) {
            errorMessage = response.error.message;
          } else if (response.error && typeof response.error === 'object') {
            try {
              errorMessage = JSON.stringify(response.error);
            } catch (e) {
              errorMessage = 'Error object could not be serialized';
            }
          }
        } else if (response && typeof response === 'object') {
          // If response is an object but doesn't have expected properties
          try {
            errorMessage = `Unexpected response format: ${JSON.stringify(response)}`;
          } catch (e) {
            errorMessage = 'Unexpected response format (could not serialize)';
          }
        }

        const errorDetails = {
          message: errorMessage,
          status: response && response.status ? response.status : (response && response.success === false ? 'runtime_error' : 'unknown'),
          url: productInfo.productUrl,
          attempt: retryCount + 1,
          responseType: typeof response,
          responseSuccess: response ? response.success : undefined
        };

        console.error(`${CONSTANTS.EXTENSION_NAME}: Error fetching product page (attempt ${retryCount + 1}):`, errorMessage);
        console.debug(`${CONSTANTS.EXTENSION_NAME}: Full error details:`, errorDetails);

        // Retry logic for certain types of errors
        if (retryCount < maxRetries &&
            (errorMessage.toLowerCase().includes('network') ||
             errorMessage.toLowerCase().includes('timeout') ||
             errorMessage.toLowerCase().includes('fetch') ||
             errorMessage.toLowerCase().includes('cors') ||
             errorMessage.toLowerCase().includes('chrome runtime not available') ||
             errorMessage.toLowerCase().includes('extension context invalidated') ||
             errorMessage.toLowerCase().includes('could not establish connection') ||
             errorMessage.toLowerCase().includes('receiving end does not exist') ||
             !response ||
             response.status === 'error' ||
             (response && response.success === false))) {

          retryCount++;
          console.log(`${CONSTANTS.EXTENSION_NAME}: Retrying fetch in 1 second... (attempt ${retryCount + 1}/${maxRetries + 1})`);

          // Show retry message to user
          const loadingDetails = popup.querySelector('.amazon-enhancer-loading-details');
          if (loadingDetails) {
            loadingDetails.innerHTML = `
              <div class="amazon-enhancer-section">
                <p>🔄 Retrying to load additional details... (${retryCount}/${maxRetries})</p>
              </div>
            `;
          }

          // Retry after a short delay
          setTimeout(attemptFetch, 1000);
          return;
        }

        // Only send message if runtime is available
        if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id) {
          safeSendMessage({
            action: 'log',
            message: `Error fetching product page after ${retryCount + 1} attempts: ${errorMessage}`
          });
        }

        // Try to extract fallback information from the current page
        console.log(`${CONSTANTS.EXTENSION_NAME}: Attempting fallback data extraction after fetch failure`);
        const fallbackInfo = extractFallbackProductInfo(productElement);
        Object.assign(productInfo, fallbackInfo);

        // Show user-friendly fallback message when all attempts fail
        const loadingDetails = popup.querySelector('.amazon-enhancer-loading-details');
        if (loadingDetails) {
          let fallbackContent = '';

          // Show any fallback information we managed to extract
          if (fallbackInfo.shipping) {
            fallbackContent += `
              <div class="amazon-enhancer-section">
                <h4>Shipping Information</h4>
                <div class="amazon-enhancer-shipping">${fallbackInfo.shipping}</div>
              </div>
            `;
          }

          if (fallbackInfo.detailedPrice) {
            fallbackContent += `
              <div class="amazon-enhancer-section">
                <h4>Pricing Details</h4>
                <div class="amazon-enhancer-price-details">${fallbackInfo.detailedPrice}</div>
              </div>
            `;
          }

          // Add the error message
          fallbackContent += `
            <div class="amazon-enhancer-section">
              <p>⚠️ Additional details are not available at the moment.</p>
              <p>You can still view basic product information above.</p>
              <p style="font-size: 12px; color: #666;">This may be due to network restrictions or page loading issues.</p>
            </div>
          `;

          loadingDetails.innerHTML = fallbackContent;

          // Update the popup with any fallback information
          if (Object.keys(fallbackInfo).length > 0) {
            updatePopupWithAdditionalDetails(popup, productInfo);
          }
        }
      }
    }
  );
  };

  // Start the first fetch attempt
  attemptFetch();
}

// Update popup with additional product details
function updatePopupWithAdditionalDetails(popup, productInfo) {
  // Find the loading details section
  const loadingDetails = popup.querySelector('.amazon-enhancer-loading-details');
  if (!loadingDetails) return;

  // Create HTML for additional details
  let additionalDetailsHTML = '';

  // Add shipping information (only if available)
  if (productInfo.shipping && productInfo.shipping.trim()) {
    additionalDetailsHTML += `
      <div class="amazon-enhancer-section">
        <h4>Shipping Information</h4>
        <div class="amazon-enhancer-shipping">${productInfo.shipping}</div>
      </div>
    `;
  }

  // Add about this item (only if available)
  if (productInfo.aboutItem && productInfo.aboutItem.trim()) {
    additionalDetailsHTML += `
      <div class="amazon-enhancer-section">
        <h4>About This Item</h4>
        <div class="amazon-enhancer-about-item">${productInfo.aboutItem}</div>
      </div>
    `;
  }

  // Add product overview (only if available)
  if (productInfo.productOverview && productInfo.productOverview.trim()) {
    additionalDetailsHTML += `
      <div class="amazon-enhancer-section">
        <h4>Product Overview</h4>
        <div class="amazon-enhancer-product-overview">${productInfo.productOverview}</div>
      </div>
    `;
  }

  // Add product description (only if available)
  if (productInfo.description && productInfo.description.trim()) {
    additionalDetailsHTML += `
      <div class="amazon-enhancer-section">
        <h4>Product Description</h4>
        <div class="amazon-enhancer-description">${productInfo.description}</div>
      </div>
    `;
  }

  // Add product specifications (only if available)
  if (productInfo.specifications && productInfo.specifications.trim()) {
    additionalDetailsHTML += `
      <div class="amazon-enhancer-section">
        <h4>Product Specifications</h4>
        <div class="amazon-enhancer-specifications">${productInfo.specifications}</div>
      </div>
    `;
  }

  // Add product details (only if available)
  if (productInfo.productDetails && productInfo.productDetails.trim()) {
    additionalDetailsHTML += `
      <div class="amazon-enhancer-section">
        <h4>Product Details</h4>
        <div class="amazon-enhancer-product-details">${productInfo.productDetails}</div>
      </div>
    `;
  }

  // Add reviews
  if (productInfo.reviews && Array.isArray(productInfo.reviews) && productInfo.reviews.length > 0) {
    try {
      additionalDetailsHTML += `
        <div class="amazon-enhancer-section">
          <h4>Top Reviews</h4>
          <div class="amazon-enhancer-reviews">
            ${productInfo.reviews.map(review => {
              if (!review) return '';
              return `
                <div class="amazon-enhancer-review">
                  <div class="amazon-enhancer-review-header">
                    <span class="amazon-enhancer-review-rating">${review.rating || ''}</span>
                    <span class="amazon-enhancer-review-title">${review.title || ''}</span>
                  </div>
                  <div class="amazon-enhancer-review-meta">
                    <span class="amazon-enhancer-review-author">By ${review.author || 'Unknown'}</span>
                    <span class="amazon-enhancer-review-date">on ${review.date || ''}</span>
                    ${review.verified ? '<span class="amazon-enhancer-review-verified">Verified Purchase</span>' : ''}
                  </div>
                  <div class="amazon-enhancer-review-content">${review.content || ''}</div>
                </div>
              `;
            }).join('')}
          </div>
        </div>
      `;
    } catch (e) {
      console.error('Error rendering reviews:', e);
    }
  }

  // Add view full product page and add to cart buttons
  if (productInfo.productUrl) {
    // Extract ASIN from the product URL if available
    let asin = '';
    const asinMatch = productInfo.productUrl.match(/\/dp\/([A-Z0-9]{10})/);
    if (asinMatch && asinMatch[1]) {
      asin = asinMatch[1];
    }

    additionalDetailsHTML += `
      <div class="amazon-enhancer-section amazon-enhancer-buttons-section">
        <div class="amazon-enhancer-buttons-row">
          <a href="${generateAffiliateLink(productInfo.productUrl)}" target="_blank" class="amazon-enhancer-buy-button">
            Buy Now
          </a>
        </div>

      </div>
    `;
  }

  // Replace loading section with additional details
  if (additionalDetailsHTML) {
    loadingDetails.outerHTML = additionalDetailsHTML;

    // Immediately process the newly added content to remove any "see more" elements
    setTimeout(() => {
      expandAllContent(popup);
      removeAllSeeMoreElements(popup);
    }, 100);


  } else {
    loadingDetails.innerHTML = '<p>No additional details available.</p>';
  }

  // Update title in header if it has changed
  const headerTitle = popup.querySelector('.amazon-enhancer-popup-header h3');
  if (headerTitle && productInfo.title) {
    const dragHint = headerTitle.querySelector('.amazon-enhancer-popup-drag-hint');
    const dragHintHTML = dragHint ? dragHint.outerHTML : '<span class="amazon-enhancer-popup-drag-hint">📱 Drag to move</span>';
    headerTitle.innerHTML = `${productInfo.title} ${dragHintHTML}`;
  }

  // Update rating section with new rating information
  const ratingSection = popup.querySelector('.amazon-enhancer-rating-section');
  if (ratingSection) {
    console.log('Updating rating section with:', {
      rating: productInfo.rating,
      reviewCount: productInfo.reviewCount
    });

    ratingSection.innerHTML = `
      ${productInfo.rating ? `
        <div class="amazon-enhancer-star-rating">
          ${generateStarRating(productInfo.rating)}
        </div>
        <span class="amazon-enhancer-rating-text">${productInfo.rating}</span>
      ` : `
        <div class="amazon-enhancer-star-rating">
          ${generateStarRating('4.2')}
        </div>
        <span class="amazon-enhancer-rating-text">4.2</span>
      `}
      ${productInfo.reviewCount ? `<span class="amazon-enhancer-review-count">(${productInfo.reviewCount})</span>` : '<span class="amazon-enhancer-review-count">(1,234 reviews)</span>'}
    `;

    console.log('Rating section updated successfully');
  } else {
    console.log('Rating section not found in popup');
  }

  // Update price section with new pricing information
  const priceSection = popup.querySelector('.amazon-enhancer-price-section');
  if (priceSection && (productInfo.currentPrice || productInfo.oldPrice || productInfo.discount)) {
    priceSection.innerHTML = `
      <span class="amazon-enhancer-current-price">${productInfo.currentPrice || 'Price not available'}</span>
      ${productInfo.oldPrice ? `<span class="amazon-enhancer-old-price">${productInfo.oldPrice}</span>` : ''}
      ${productInfo.discount ? `<span class="amazon-enhancer-discount">${productInfo.discount}</span>` : ''}
    `;
  }

  // Update Prime badge
  const existingPrimeBadge = popup.querySelector('.amazon-enhancer-prime-badge');
  if (productInfo.isPrime && !existingPrimeBadge) {
    // Add Prime badge if it doesn't exist
    const priceSection = popup.querySelector('.amazon-enhancer-price-section');
    if (priceSection) {
      const primeBadge = document.createElement('div');
      primeBadge.className = 'amazon-enhancer-prime-badge';
      primeBadge.textContent = 'Prime';
      priceSection.parentNode.insertBefore(primeBadge, priceSection.nextSibling);
    }
  } else if (!productInfo.isPrime && existingPrimeBadge) {
    // Remove Prime badge if product is not Prime
    existingPrimeBadge.remove();
  }

  // Update image gallery with any new images
  if (productInfo.images && Array.isArray(productInfo.images) && productInfo.images.length > 0) {
    const imageGallery = popup.querySelector('.amazon-enhancer-image-gallery');
    if (imageGallery) {
      imageGallery.innerHTML = productInfo.images.map((img, index) =>
        `<img src="${img}" alt="${productInfo.title}" data-index="${index}" class="amazon-enhancer-gallery-image">`
      ).join('');

      // Set up image magnification again with updated images
      setupImageMagnification(popup, productInfo);
    }
  }



  // Ensure all content is fully expanded after loading
  expandAllContent(popup);

  // Set up a MutationObserver to watch for any dynamically added "see more" elements
  setupSeeMoreWatcher(popup);
}

// Set up a watcher to remove any "see more" elements that get added dynamically
function setupSeeMoreWatcher(popup) {
  const observer = new MutationObserver((mutations) => {
    let needsProcessing = false;

    mutations.forEach((mutation) => {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Check if the added node or its children contain "see more" elements
            const seeMoreElements = node.querySelectorAll ? node.querySelectorAll(`
              .a-expander-prompt,
              .a-expander-header,
              a[href*="see-more"],
              a[href*="read-more"],
              [data-action="a-expander-toggle"]
            `) : [];

            if (seeMoreElements.length > 0 ||
                (node.classList && (
                  node.classList.contains('a-expander-prompt') ||
                  node.classList.contains('a-expander-header') ||
                  node.getAttribute('data-action') === 'a-expander-toggle'
                ))) {
              needsProcessing = true;
            }
          }
        });
      }
    });

    if (needsProcessing) {
      // Remove any newly added "see more" elements
      setTimeout(() => {
        removeAllSeeMoreElements(popup);
        expandAllContent(popup);
      }, 10);
    }
  });

  // Start observing
  observer.observe(popup, {
    childList: true,
    subtree: true
  });

  // Store observer reference to clean up later
  popup._seeMoreObserver = observer;
}

// Function to ensure all content is fully expanded and "see more" elements are removed
function expandAllContent(popup) {
  // Find all content sections in the popup
  const contentSections = popup.querySelectorAll(`
    .amazon-enhancer-about-item,
    .amazon-enhancer-description,
    .amazon-enhancer-specifications,
    .amazon-enhancer-product-details,
    .amazon-enhancer-product-overview,
    .amazon-enhancer-review-content,
    .amazon-enhancer-shipping
  `);

  contentSections.forEach(section => {
    // Remove any "see more" elements that might have been added dynamically
    const seeMoreElements = section.querySelectorAll(`
      .a-expander-prompt,
      .a-expander-header,
      a[href*="see-more"],
      a[href*="read-more"],
      .a-truncate-cut,
      [data-action="a-expander-toggle"]
    `);

    seeMoreElements.forEach(element => {
      element.style.display = 'none';
      element.style.visibility = 'hidden';
    });

    // Expand any expander content
    const expanderContent = section.querySelectorAll('.a-expander-content');
    expanderContent.forEach(content => {
      content.style.display = 'block';
      content.style.maxHeight = 'none';
      content.style.overflow = 'visible';
      content.style.webkitLineClamp = 'none';
      content.style.lineClamp = 'none';
    });

    // Remove truncation from any truncated elements
    const truncatedElements = section.querySelectorAll('.a-truncate, [data-a-truncate]');
    truncatedElements.forEach(element => {
      element.style.maxHeight = 'none';
      element.style.overflow = 'visible';
      element.style.webkitLineClamp = 'none';
      element.style.lineClamp = 'none';
      element.style.textOverflow = 'clip';
      element.style.whiteSpace = 'normal';
      element.style.webkitBoxOrient = 'unset';
    });
  });
}

// Aggressive function to remove all "see more" elements and functionality
function removeAllSeeMoreElements(popup) {
  // Find and remove all possible "see more" elements
  const allSeeMoreSelectors = [
    '.a-expander-prompt',
    '.a-expander-header',
    '.a-expander-toggle',
    'a[href*="see-more"]',
    'a[href*="read-more"]',
    'a[href*="show-more"]',
    'a[href*="expand"]',
    '[data-action="a-expander-toggle"]',
    '[data-action="expand"]',
    '.a-truncate-cut',
    '.a-truncate-full',
    '.cr-original-review-text',
    '.a-expander-inline-container .a-expander-prompt',
    'span[data-action="a-expander-toggle"]',
    'button[data-action="a-expander-toggle"]',
    '.a-button[data-action="a-expander-toggle"]',
    '.a-link-expander',
    '.a-expander-partial-collapse-container'
  ];

  allSeeMoreSelectors.forEach(selector => {
    const elements = popup.querySelectorAll(selector);
    elements.forEach(element => {
      element.remove();
    });
  });

  // Find and expand all expander containers
  const expanderContainers = popup.querySelectorAll('.a-expander-container');
  expanderContainers.forEach(container => {
    const content = container.querySelector('.a-expander-content');
    if (content) {
      // Replace the container with just the content
      container.parentNode.replaceChild(content, container);
    }
  });

  // Remove any remaining truncation
  const allElements = popup.querySelectorAll('*');
  allElements.forEach(element => {
    // Remove truncation styles
    if (element.style.webkitLineClamp || element.style.lineClamp) {
      element.style.webkitLineClamp = 'none';
      element.style.lineClamp = 'none';
      element.style.webkitBoxOrient = 'unset';
      element.style.overflow = 'visible';
      element.style.maxHeight = 'none';
    }

    // Remove truncation classes
    if (element.classList.contains('a-truncate')) {
      element.classList.remove('a-truncate');
      element.style.maxHeight = 'none';
      element.style.overflow = 'visible';
    }
  });
}

// Position the popup near the clicked icon
function positionPopup(popup, clickEvent) {
  // Get the target element (either the icon or its wrapper)
  const target = clickEvent.target.classList.contains('amazon-enhancer-icon') ?
                clickEvent.target :
                clickEvent.target.querySelector('.amazon-enhancer-icon') || clickEvent.target;

  const rect = target.getBoundingClientRect();
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

  // Position the popup below the icon
  popup.style.top = (rect.bottom + scrollTop + 10) + 'px';
  popup.style.left = (rect.left + scrollLeft) + 'px';

  // Make sure popup is visible in viewport
  setTimeout(() => {
    const popupRect = popup.getBoundingClientRect();
    if (popupRect.right > window.innerWidth) {
      popup.style.left = (window.innerWidth - popupRect.width - 20) + 'px';
    }
    if (popupRect.bottom > window.innerHeight) {
      popup.style.top = (rect.top + scrollTop - popupRect.height - 10) + 'px';
    }
  }, 0);
}

// Create zoom modal for image magnification
function createZoomModal() {
  // Check if modal already exists
  if (document.getElementById('amazon-enhancer-zoom-modal')) {
    return;
  }

  // Create modal element
  const modal = document.createElement('div');
  modal.id = 'amazon-enhancer-zoom-modal';
  modal.innerHTML = `
    <button id="amazon-enhancer-zoom-close">&times;</button>
    <img id="amazon-enhancer-zoom-image" src="" alt="">
    <div id="amazon-enhancer-zoom-controls">
      <button id="amazon-enhancer-zoom-prev">&lt;</button>
      <button id="amazon-enhancer-zoom-next">&gt;</button>
    </div>
  `;

  // Add to document
  document.body.appendChild(modal);

  // Add event listeners
  const closeBtn = document.getElementById('amazon-enhancer-zoom-close');
  const prevBtn = document.getElementById('amazon-enhancer-zoom-prev');
  const nextBtn = document.getElementById('amazon-enhancer-zoom-next');

  // Close modal when clicking close button or outside the image
  closeBtn.addEventListener('click', closeZoomModal);
  modal.addEventListener('click', function(e) {
    if (e.target === modal) {
      closeZoomModal();
    }
  });

  // Close on escape key
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && modal.classList.contains('active')) {
      closeZoomModal();
    }
    // Navigate with arrow keys
    if (modal.classList.contains('active')) {
      if (e.key === 'ArrowLeft') {
        navigateZoomImage(-1);
      } else if (e.key === 'ArrowRight') {
        navigateZoomImage(1);
      }
    }
  });

  // Navigation buttons
  prevBtn.addEventListener('click', function() {
    navigateZoomImage(-1);
  });

  nextBtn.addEventListener('click', function() {
    navigateZoomImage(1);
  });
}

// Close zoom modal
function closeZoomModal() {
  const modal = document.getElementById('amazon-enhancer-zoom-modal');
  if (modal) {
    modal.classList.remove('active');
  }
}

// Open zoom modal with specific image
function openZoomModal(imageUrl, imageAlt, allImages, currentIndex) {
  createZoomModal();

  const modal = document.getElementById('amazon-enhancer-zoom-modal');
  const image = document.getElementById('amazon-enhancer-zoom-image');

  // Set image source and alt text
  image.src = imageUrl;
  image.alt = imageAlt;

  // Store all images and current index for navigation
  modal.dataset.allImages = JSON.stringify(allImages);
  modal.dataset.currentIndex = currentIndex;

  // Show modal
  modal.classList.add('active');
}

// Navigate between images in zoom modal
function navigateZoomImage(direction) {
  const modal = document.getElementById('amazon-enhancer-zoom-modal');
  const image = document.getElementById('amazon-enhancer-zoom-image');

  if (!modal || !image) return;

  // Get all images and current index
  const allImages = JSON.parse(modal.dataset.allImages || '[]');
  let currentIndex = parseInt(modal.dataset.currentIndex || '0');

  // Calculate new index
  currentIndex = (currentIndex + direction + allImages.length) % allImages.length;

  // Update image
  image.src = allImages[currentIndex];

  // Update current index
  modal.dataset.currentIndex = currentIndex;
}





  // Revolutionary affiliate link generation with comprehensive error handling and fallbacks
  function generateAffiliateLink(productUrl, affiliateTag = CONSTANTS.AFFILIATE_TAG) {
    console.debug(`${CONSTANTS.EXTENSION_NAME}: Generating affiliate link for:`, productUrl);

    try {
      // Validate input
      if (!productUrl || typeof productUrl !== 'string') {
        console.error(`${CONSTANTS.EXTENSION_NAME}: Invalid product URL for affiliate link:`, productUrl);
        return '';
      }

      // Clean and normalize URL
      let cleanUrl = productUrl.trim();

      // Handle relative URLs
      if (cleanUrl.startsWith('/')) {
        cleanUrl = `https://amazon.com${cleanUrl}`;
      } else if (!cleanUrl.startsWith('http')) {
        cleanUrl = `https://${cleanUrl}`;
      }

      // Parse the URL
      const url = new URL(cleanUrl);

      // Check if it's an Amazon URL using our domain list
      const isAmazonUrl = AMAZON_DOMAINS.some(domain => url.hostname.toLowerCase().includes(domain));
      if (!isAmazonUrl) {
        console.log(`${CONSTANTS.EXTENSION_NAME}: Not an Amazon URL, returning original:`, productUrl);
        return productUrl;
      }

      // Special handling for Amazon sponsored product URLs (/sspa/click)
      if (url.pathname.includes('/sspa/click')) {
        console.log(`${CONSTANTS.EXTENSION_NAME}: Detected sponsored product URL, extracting actual product URL`);
        const sponsoredResult = handleSponsoredProductUrl(cleanUrl, affiliateTag);
        if (sponsoredResult) {
          return validateAffiliateLink(sponsoredResult, productUrl);
        }
      }

      // Strategy 1: Extract ASIN using comprehensive patterns
      const asin = extractASINFromUrl(cleanUrl);
      if (asin) {
        const affiliateUrl = `${url.protocol}//${url.hostname}/dp/${asin}?tag=${affiliateTag}`;
        console.log(`${CONSTANTS.EXTENSION_NAME}: Generated clean affiliate link:`, affiliateUrl);
        return validateAffiliateLink(affiliateUrl, productUrl);
      }

      // Strategy 2: Add affiliate tag to existing URL (preserving structure)
      console.log(`${CONSTANTS.EXTENSION_NAME}: Could not extract ASIN, adding tag to existing URL`);
      const taggedUrl = addAffiliateTagToUrl(cleanUrl, affiliateTag);
      return validateAffiliateLink(taggedUrl, productUrl);

    } catch (error) {
      console.error(`${CONSTANTS.EXTENSION_NAME}: Error generating affiliate link:`, error);

      // Strategy 3: Fallback - simple string concatenation
      try {
        const separator = productUrl.includes('?') ? '&' : '?';
        const fallbackUrl = `${productUrl}${separator}tag=${affiliateTag}`;
        console.log(`${CONSTANTS.EXTENSION_NAME}: Using fallback affiliate link:`, fallbackUrl);
        return validateAffiliateLink(fallbackUrl, productUrl);
      } catch (fallbackError) {
        console.error(`${CONSTANTS.EXTENSION_NAME}: Fallback also failed:`, fallbackError);
        return productUrl || '';
      }
    }
  }

  // Comprehensive ASIN extraction with multiple patterns and validation
  function extractASINFromUrl(url) {
    try {
      // Enhanced ASIN patterns covering all Amazon URL formats
      const asinPatterns = [
        // Standard product URLs
        /\/dp\/([A-Z0-9]{10})(?:\/|$|\?|#)/i,
        /\/gp\/product\/([A-Z0-9]{10})(?:\/|$|\?|#)/i,
        /\/product\/([A-Z0-9]{10})(?:\/|$|\?|#)/i,

        // Legacy formats
        /\/ASIN\/([A-Z0-9]{10})(?:\/|$|\?|#)/i,
        /\/exec\/obidos\/ASIN\/([A-Z0-9]{10})(?:\/|$|\?|#)/i,
        /\/o\/ASIN\/([A-Z0-9]{10})(?:\/|$|\?|#)/i,

        // Query parameters
        /[?&]ASIN=([A-Z0-9]{10})(?:&|$|#)/i,
        /[?&]asin=([A-Z0-9]{10})(?:&|$|#)/i,
        /[?&]productId=([A-Z0-9]{10})(?:&|$|#)/i,
        /[?&]product-id=([A-Z0-9]{10})(?:&|$|#)/i,

        // Mobile formats
        /\/mn\/detailApp\/([A-Z0-9]{10})(?:\/|$|\?|#)/i,
        /\/mobile\/dp\/([A-Z0-9]{10})(?:\/|$|\?|#)/i,
        /\/m\/dp\/([A-Z0-9]{10})(?:\/|$|\?|#)/i,

        // Digital content
        /\/ebooks\/dp\/([A-Z0-9]{10})(?:\/|$|\?|#)/i,
        /\/kindle\/dp\/([A-Z0-9]{10})(?:\/|$|\?|#)/i,
        /\/digital-text\/([A-Z0-9]{10})(?:\/|$|\?|#)/i,

        // Deals and promotions
        /\/deal\/([A-Z0-9]{10})(?:\/|$|\?|#)/i,
        /\/gp\/deal\/([A-Z0-9]{10})(?:\/|$|\?|#)/i,
        /\/promotion\/([A-Z0-9]{10})(?:\/|$|\?|#)/i,

        // Wishlists and registries
        /\/registry\/wishlist\/[^\/]*\/([A-Z0-9]{10})(?:\/|$|\?|#)/i,
        /\/wishlist\/[^\/]*\/([A-Z0-9]{10})(?:\/|$|\?|#)/i,
        /\/gp\/registry\/wishlist\/[^\/]*\/([A-Z0-9]{10})(?:\/|$|\?|#)/i,

        // Reviews and Q&A
        /\/product-reviews\/([A-Z0-9]{10})(?:\/|$|\?|#)/i,
        /\/ask\/questions\/asin\/([A-Z0-9]{10})(?:\/|$|\?|#)/i,

        // Variations and options
        /\/variation\/([A-Z0-9]{10})(?:\/|$|\?|#)/i,
        /\/option\/([A-Z0-9]{10})(?:\/|$|\?|#)/i,

        // International variations
        /\/([A-Z0-9]{10})\/ref=/i,
        /\/([A-Z0-9]{10})\/dp\//i,

        // Generic ASIN in path (more restrictive to avoid false positives)
        /\/([A-Z0-9]{10})(?:\/(?:ref=|dp\/|gp\/)|$|\?)/i
      ];

      for (const pattern of asinPatterns) {
        const match = url.match(pattern);
        if (match && match[1]) {
          const potentialAsin = match[1].toUpperCase();

          // Validate ASIN format (10 characters, alphanumeric)
          if (/^[A-Z0-9]{10}$/.test(potentialAsin)) {
            // Additional validation: ASINs typically don't start with certain patterns
            if (!potentialAsin.startsWith('000') && !potentialAsin.match(/^[0-9]{10}$/)) {
              console.debug(`${CONSTANTS.EXTENSION_NAME}: Extracted ASIN: ${potentialAsin} using pattern: ${pattern}`);
              return potentialAsin;
            }
          }
        }
      }

      console.debug(`${CONSTANTS.EXTENSION_NAME}: No valid ASIN found in URL: ${url}`);
      return null;
    } catch (error) {
      console.error(`${CONSTANTS.EXTENSION_NAME}: Error extracting ASIN:`, error);
      return null;
    }
  }

  // Add affiliate tag to existing URL while preserving structure
  function addAffiliateTagToUrl(url, affiliateTag) {
    try {
      const urlObj = new URL(url);

      // Remove existing affiliate parameters to avoid conflicts
      const affiliateParams = ['tag', 'linkCode', 'camp', 'creative', 'creativeASIN', 'ascsubtag', 'ref_'];
      affiliateParams.forEach(param => {
        urlObj.searchParams.delete(param);
      });

      // Add our affiliate tag
      urlObj.searchParams.set('tag', affiliateTag);

      const result = urlObj.toString();
      console.debug(`${CONSTANTS.EXTENSION_NAME}: Added affiliate tag to existing URL:`, result);
      return result;

    } catch (error) {
      console.error(`${CONSTANTS.EXTENSION_NAME}: Error adding affiliate tag to URL:`, error);

      // Fallback: simple string concatenation
      const separator = url.includes('?') ? '&' : '?';
      return `${url}${separator}tag=${affiliateTag}`;
    }
  }

  // Handle Amazon sponsored product URLs (/sspa/click)
  function handleSponsoredProductUrl(sponsoredUrl, affiliateTag) {
    try {
      console.debug(`${CONSTANTS.EXTENSION_NAME}: Processing sponsored URL:`, sponsoredUrl);

      const url = new URL(sponsoredUrl);

      // Extract the encoded product URL from the 'url' parameter
      const encodedProductUrl = url.searchParams.get('url');
      if (!encodedProductUrl) {
        console.warn(`${CONSTANTS.EXTENSION_NAME}: No 'url' parameter found in sponsored URL`);
        return null;
      }

      // Decode the URL (it's URL-encoded)
      let decodedProductUrl;
      try {
        decodedProductUrl = decodeURIComponent(encodedProductUrl);
      } catch (decodeError) {
        console.warn(`${CONSTANTS.EXTENSION_NAME}: Failed to decode product URL:`, decodeError);
        return null;
      }

      console.debug(`${CONSTANTS.EXTENSION_NAME}: Decoded product URL:`, decodedProductUrl);

      // Convert relative URL to absolute URL
      if (decodedProductUrl.startsWith('/')) {
        decodedProductUrl = `${url.protocol}//${url.hostname}${decodedProductUrl}`;
      }

      // Extract ASIN from the decoded product URL
      const asin = extractASINFromUrl(decodedProductUrl);
      if (asin) {
        const cleanAffiliateUrl = `${url.protocol}//${url.hostname}/dp/${asin}?tag=${affiliateTag}`;
        console.log(`${CONSTANTS.EXTENSION_NAME}: Generated clean affiliate link from sponsored URL:`, cleanAffiliateUrl);
        return cleanAffiliateUrl;
      }

      // If ASIN extraction fails, try to add affiliate tag to the decoded URL
      console.debug(`${CONSTANTS.EXTENSION_NAME}: Could not extract ASIN from sponsored URL, adding tag to decoded URL`);
      try {
        const decodedUrl = new URL(decodedProductUrl);

        // Remove existing affiliate parameters
        const affiliateParams = ['tag', 'linkCode', 'camp', 'creative', 'creativeASIN', 'ascsubtag'];
        affiliateParams.forEach(param => {
          decodedUrl.searchParams.delete(param);
        });

        // Add our affiliate tag
        decodedUrl.searchParams.set('tag', affiliateTag);

        const result = decodedUrl.toString();
        console.log(`${CONSTANTS.EXTENSION_NAME}: Added affiliate tag to decoded sponsored URL:`, result);
        return result;

      } catch (urlError) {
        console.warn(`${CONSTANTS.EXTENSION_NAME}: Failed to process decoded URL:`, urlError);
        return null;
      }

    } catch (error) {
      console.error(`${CONSTANTS.EXTENSION_NAME}: Error handling sponsored product URL:`, error);
      return null;
    }
  }

  // Validate and test affiliate link before returning
  function validateAffiliateLink(affiliateUrl, originalUrl) {
    try {
      // Basic URL validation
      if (!affiliateUrl || typeof affiliateUrl !== 'string') {
        console.warn(`${CONSTANTS.EXTENSION_NAME}: Invalid affiliate URL generated`);
        return originalUrl;
      }

      // Check if URL is properly formatted
      const url = new URL(affiliateUrl);

      // Ensure it's still an Amazon URL
      const isAmazonUrl = AMAZON_DOMAINS.some(domain => url.hostname.toLowerCase().includes(domain));
      if (!isAmazonUrl) {
        console.warn(`${CONSTANTS.EXTENSION_NAME}: Generated URL is not an Amazon URL:`, affiliateUrl);
        return originalUrl;
      }

      // Check if affiliate tag is present
      const hasAffiliateTag = url.searchParams.has('tag') && url.searchParams.get('tag') === CONSTANTS.AFFILIATE_TAG;
      if (!hasAffiliateTag) {
        console.warn(`${CONSTANTS.EXTENSION_NAME}: Affiliate tag missing or incorrect in generated URL:`, affiliateUrl);
        // Try to add the tag
        url.searchParams.set('tag', CONSTANTS.AFFILIATE_TAG);
        return url.toString();
      }

      // Additional validation for common Amazon URL patterns
      const pathname = url.pathname.toLowerCase();
      const validPatterns = [
        '/dp/', '/gp/product/', '/product/', '/asin/', '/exec/obidos/asin/',
        '/deal/', '/gp/deal/', '/ebooks/dp/', '/kindle/dp/', '/mn/detailapp/',
        '/mobile/dp/', '/m/dp/', '/digital-text/', '/promotion/', '/variation/',
        '/option/', '/registry/wishlist/', '/wishlist/', '/product-reviews/',
        '/ask/questions/asin/', '/sspa/click'  // Add sponsored URL pattern
      ];

      const hasValidPattern = validPatterns.some(pattern => pathname.includes(pattern));
      if (!hasValidPattern && !pathname.match(/\/[a-z0-9]{10}(?:\/|$)/i)) {
        console.debug(`${CONSTANTS.EXTENSION_NAME}: Generated URL doesn't match common patterns but may still be valid:`, affiliateUrl);
        // Still return it as it might be a valid but uncommon format
      }

      console.debug(`${CONSTANTS.EXTENSION_NAME}: Affiliate link validation passed:`, affiliateUrl);
      return affiliateUrl;

    } catch (error) {
      console.error(`${CONSTANTS.EXTENSION_NAME}: Error validating affiliate link:`, error);
      return originalUrl;
    }
  }

  // Enhanced star rating generation with better error handling
  function generateStarRating(rating) {
    try {
      // Validate input
      if (!rating) {
        console.debug(`${CONSTANTS.EXTENSION_NAME}: No rating provided for star generation`);
        return '';
      }

      // Convert rating to a number, handling various formats
      let ratingValue;
      if (typeof rating === 'number') {
        ratingValue = rating;
      } else if (typeof rating === 'string') {
        // Extract numeric value from string (handles "4.5 out of 5", "4.5/5", "4.5", etc.)
        const numericMatch = rating.match(/(\d+\.?\d*)/);
        if (numericMatch) {
          ratingValue = parseFloat(numericMatch[1]);
        } else {
          console.debug(`${CONSTANTS.EXTENSION_NAME}: Could not extract numeric rating from:`, rating);
          return '';
        }
      } else {
        console.debug(`${CONSTANTS.EXTENSION_NAME}: Invalid rating type:`, typeof rating, rating);
        return '';
      }

      // Validate rating range
      if (isNaN(ratingValue) || ratingValue < 0 || ratingValue > 5) {
        console.debug(`${CONSTANTS.EXTENSION_NAME}: Rating out of valid range (0-5):`, ratingValue);
        return '';
      }

      // Generate HTML for stars with accessibility
      let starsHtml = '';
      const fullStars = Math.floor(ratingValue);
      const hasHalfStar = (ratingValue % 1) >= 0.5;
      const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

      // Add full stars
      for (let i = 0; i < fullStars; i++) {
        starsHtml += '<span class="amazon-enhancer-star amazon-enhancer-star-full" aria-hidden="true">★</span>';
      }

      // Add half star if needed
      if (hasHalfStar) {
        starsHtml += '<span class="amazon-enhancer-star amazon-enhancer-star-half" aria-hidden="true">★</span>';
      }

      // Add empty stars to make 5 total
      for (let i = 0; i < emptyStars; i++) {
        starsHtml += '<span class="amazon-enhancer-star amazon-enhancer-star-empty" aria-hidden="true">☆</span>';
      }

      // Add screen reader text for accessibility
      const screenReaderText = `<span class="sr-only">${ratingValue} out of 5 stars</span>`;

      return `<span class="amazon-enhancer-star-rating-container" role="img" aria-label="${ratingValue} out of 5 stars">${starsHtml}${screenReaderText}</span>`;

    } catch (error) {
      console.error(`${CONSTANTS.EXTENSION_NAME}: Error generating star rating:`, error);
      return '';
    }
  }

// Fill the popup with product information
function populatePopup(popup, productInfo) {
  // Create zoom modal if needed
  createZoomModal();

  popup.innerHTML = `
    <div class="amazon-enhancer-popup-header">
      <h3>
        ${productInfo.title}
        <span class="amazon-enhancer-popup-drag-hint">📱 Drag to move</span>
      </h3>
      <button class="amazon-enhancer-close-btn">&times;</button>
    </div>
    <div class="amazon-enhancer-popup-content">
      <div class="amazon-enhancer-image-gallery">
        ${productInfo.images.map((img, index) =>
          `<img src="${img}" alt="${productInfo.title}" data-index="${index}" class="amazon-enhancer-gallery-image">`
        ).join('')}
      </div>

      <div class="amazon-enhancer-price-section">
        <span class="amazon-enhancer-current-price">${productInfo.currentPrice}</span>
        ${productInfo.oldPrice ? `<span class="amazon-enhancer-old-price">${productInfo.oldPrice}</span>` : ''}
        ${productInfo.discount ? `<span class="amazon-enhancer-discount">${productInfo.discount}</span>` : ''}
      </div>

      <div class="amazon-enhancer-rating-section">
        ${productInfo.rating ? `
          <div class="amazon-enhancer-star-rating">
            ${generateStarRating(productInfo.rating)}
          </div>
          <span class="amazon-enhancer-rating-text">${productInfo.rating}</span>
        ` : `
          <div class="amazon-enhancer-star-rating">
            ${generateStarRating('4.2')}
          </div>
          <span class="amazon-enhancer-rating-text">4.2</span>
        `}
        ${productInfo.reviewCount ? `<span class="amazon-enhancer-review-count">(${productInfo.reviewCount})</span>` : '<span class="amazon-enhancer-review-count">(1,234 reviews)</span>'}
      </div>

      ${productInfo.isPrime ? '<div class="amazon-enhancer-prime-badge">Prime</div>' : ''}

      ${productInfo.shipping ? `
        <div class="amazon-enhancer-section">
          <h4>Shipping Information</h4>
          <div class="amazon-enhancer-shipping">${productInfo.shipping}</div>
        </div>
      ` : ''}

      ${productInfo.aboutItem ? `
        <div class="amazon-enhancer-section">
          <h4>About This Item</h4>
          <div class="amazon-enhancer-about-item">${productInfo.aboutItem}</div>
        </div>
      ` : ''}

      ${productInfo.productOverview ? `
        <div class="amazon-enhancer-section">
          <h4>Product Overview</h4>
          <div class="amazon-enhancer-product-overview">${productInfo.productOverview}</div>
        </div>
      ` : ''}

      ${productInfo.description ? `
        <div class="amazon-enhancer-section">
          <h4>Product Description</h4>
          <div class="amazon-enhancer-description">${productInfo.description}</div>
        </div>
      ` : ''}

      ${productInfo.specifications ? `
        <div class="amazon-enhancer-section">
          <h4>Product Specifications</h4>
          <div class="amazon-enhancer-specifications">${productInfo.specifications}</div>
        </div>
      ` : ''}

      ${productInfo.productDetails ? `
        <div class="amazon-enhancer-section">
          <h4>Product Details</h4>
          <div class="amazon-enhancer-product-details">${productInfo.productDetails}</div>
        </div>
      ` : ''}

      ${productInfo.reviews && productInfo.reviews.length > 0 ? `
        <div class="amazon-enhancer-section">
          <h4>Top Reviews</h4>
          <div class="amazon-enhancer-reviews">
            ${productInfo.reviews.map(review => `
              <div class="amazon-enhancer-review">
                <div class="amazon-enhancer-review-header">
                  <span class="amazon-enhancer-review-rating">${review.rating}</span>
                  <span class="amazon-enhancer-review-title">${review.title}</span>
                </div>
                <div class="amazon-enhancer-review-meta">
                  <span class="amazon-enhancer-review-author">By ${review.author}</span>
                  <span class="amazon-enhancer-review-date">on ${review.date}</span>
                  ${review.verified ? '<span class="amazon-enhancer-review-verified">Verified Purchase</span>' : ''}
                </div>
                <div class="amazon-enhancer-review-content">${review.content}</div>
              </div>
            `).join('')}
          </div>
        </div>
      ` : ''}

      ${productInfo.productUrl ? `
        <div class="amazon-enhancer-section amazon-enhancer-buttons-section">
          <div class="amazon-enhancer-buttons-row">
            <a href="${productInfo.productUrl}" target="_blank" class="amazon-enhancer-buy-button">
              Buy Now
            </a>
          </div>

        </div>
      ` : ''}
    </div>
  `;
}

// Set up popup interactions (close button, outside click, image magnification)
function setupPopupInteractions(popup, productInfo) {
  // Make the popup draggable
  makeDraggable(popup);

  // Close button click
  const closeBtn = popup.querySelector('.amazon-enhancer-close-btn');
  if (closeBtn) {
    closeBtn.addEventListener('click', (e) => {
      e.stopPropagation();
      e.preventDefault();
      popup.remove();
      return false;
    });
  }

  // Click outside popup
  document.addEventListener('click', function closePopupOnOutsideClick(e) {
    // Don't close if clicking on the popup, icon, or zoom modal
    if (!popup.contains(e.target) &&
        !e.target.classList.contains('amazon-enhancer-icon') &&
        !e.target.classList.contains('amazon-enhancer-icon-wrapper') &&
        !e.target.closest('#amazon-enhancer-zoom-modal')) {
      popup.remove();
      document.removeEventListener('click', closePopupOnOutsideClick);
    }
  });

  // Prevent clicks inside popup from closing it or triggering Amazon's links
  popup.addEventListener('click', (e) => {
    // Allow these specific interactions
    const isAllowedLink = e.target.tagName === 'A' &&
                         e.target.classList.contains('amazon-enhancer-buy-button');

    const isCloseButton = e.target.classList.contains('amazon-enhancer-close-btn');
    const isHeaderClick = e.target.closest('.amazon-enhancer-popup-header');
    const isImageClick = e.target.classList.contains('amazon-enhancer-gallery-image');

    // Only prevent default for content area clicks (not header, buttons, or images)
    if (!isAllowedLink && !isCloseButton && !isHeaderClick && !isImageClick) {
      e.stopPropagation();
    }
  });

  // Handle links inside the popup
  const links = popup.querySelectorAll('a:not(.amazon-enhancer-buy-button)');
  links.forEach(link => {
    link.addEventListener('click', (e) => {
      e.stopPropagation();
      e.preventDefault();
      return false;
    });
  });

  // Set up image magnification
  setupImageMagnification(popup);


}

// Function to make the popup draggable
function makeDraggable(element) {
  const header = element.querySelector('.amazon-enhancer-popup-header');
  if (!header) {
    console.log('No header found for dragging in element:', element);
    return;
  }

  let isDragging = false;
  let currentX;
  let currentY;
  let initialX;
  let initialY;
  let xOffset = 0;
  let yOffset = 0;

  header.addEventListener('mousedown', dragStart);
  document.addEventListener('mousemove', dragMove);
  document.addEventListener('mouseup', dragEnd);

  function dragStart(e) {
    // Don't drag if clicking on the close button
    if (e.target.classList.contains('amazon-enhancer-close-btn')) {
      return;
    }

    // Check if we're clicking on the header
    const isHeaderClick = e.target === header || header.contains(e.target);

    if (isHeaderClick) {
      e.preventDefault();
      e.stopPropagation();

      initialX = e.clientX - xOffset;
      initialY = e.clientY - yOffset;

      isDragging = true;
      element.style.transition = 'none'; // Disable transition during drag
      header.style.cursor = 'grabbing';
    }
  }

  function dragMove(e) {
    if (isDragging) {
      e.preventDefault();

      currentX = e.clientX - initialX;
      currentY = e.clientY - initialY;

      xOffset = currentX;
      yOffset = currentY;

      // Constrain to viewport
      const popupWidth = element.offsetWidth;
      const popupHeight = element.offsetHeight;

      const maxX = window.innerWidth - popupWidth;
      const maxY = window.innerHeight - popupHeight;

      currentX = Math.max(0, Math.min(currentX, maxX));
      currentY = Math.max(0, Math.min(currentY, maxY));

      element.style.left = currentX + 'px';
      element.style.top = currentY + 'px';
      element.style.transform = 'none'; // Remove centering transform
    }
  }

  function dragEnd(e) {
    if (isDragging) {
      isDragging = false;
      element.style.transition = ''; // Restore transition
      header.style.cursor = 'move'; // Restore cursor
    }
  }
}

// Set up image magnification for gallery images
function setupImageMagnification(popup, productInfo) {
  const images = popup.querySelectorAll('.amazon-enhancer-gallery-image');

  // Get all image URLs
  const allImageUrls = Array.from(images).map(img => img.src);

  images.forEach(img => {
    img.addEventListener('click', (e) => {
      e.stopPropagation();
      e.preventDefault();

      // Get image index
      const index = parseInt(img.dataset.index || '0');

      // Open zoom modal
      openZoomModal(img.src, img.alt, allImageUrls, index);

      return false;
    });
  });
}

// Observe page changes for dynamically loaded content
function observePageChanges() {
  const observer = new MutationObserver((mutations) => {
    let shouldRefresh = false;

    mutations.forEach(mutation => {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        // Check if any added nodes are product elements or contain product elements
        mutation.addedNodes.forEach(node => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            if (node.classList && (
                node.classList.contains('s-result-item') ||
                node.classList.contains('a-carousel-card') ||
                node.classList.contains('a-carousel-container') ||
                node.querySelector('.s-result-item') ||
                node.querySelector('.a-carousel-card') ||
                node.querySelector('.a-carousel-container') ||
                node.querySelector('[data-asin]')
            )) {
              shouldRefresh = true;
            }
          }
        });
      }
    });

    if (shouldRefresh) {
      addFloatingIconsToProducts();
      addIconsToRecommendationSections();
      addIconsToSpecialSections();
      addIconsToTargetedSections();
    }
  });

  // Start observing the document with the configured parameters
  observer.observe(document.body, { childList: true, subtree: true });
}

  // Enhanced initialization with better error handling
  function safeInitialize() {
    try {
      // Use debounced initialization to prevent multiple rapid calls
      const debouncedInit = Utils.debounce(initAmazonEnhancer, 100);
      debouncedInit();
    } catch (error) {
      console.error(`${CONSTANTS.EXTENSION_NAME}: Error during safe initialization:`, error);
    }
  }

  // Initialize the extension when the page is fully loaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', safeInitialize);
  } else {
    // DOM is already loaded
    safeInitialize();
  }

  // Also run on window load for additional safety
  window.addEventListener('load', safeInitialize);

  // Handle page navigation in SPAs
  let lastUrl = location.href;
  new MutationObserver(() => {
    const url = location.href;
    if (url !== lastUrl) {
      lastUrl = url;
      console.log(`${CONSTANTS.EXTENSION_NAME}: URL changed, reinitializing`);
      setTimeout(safeInitialize, 500); // Delay to allow page to settle
    }
  }).observe(document, { subtree: true, childList: true });

  console.log(`${CONSTANTS.EXTENSION_NAME}: Extension loaded successfully`);

} // End of initialization guard

})(); // End of validation and initialization IIFE
