// Popup script for affliater

document.addEventListener('DOMContentLoaded', function() {
  // Get status text element
  const statusText = document.getElementById('status-text');

  // Check if we're on an Amazon page
  chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
    const currentTab = tabs[0];

    if (currentTab && currentTab.url) {
      if (currentTab.url.includes('amazon.com')) {
        statusText.textContent = 'Active on this Amazon page. Look for the info icon on product images!';
      } else {
        statusText.textContent = 'This extension only works on Amazon.com pages.';
      }
    }
  });

  // Load Excel data statistics
  loadExcelStats();

  // Set up event listeners
  setupEventListeners();
});

// Load Excel data statistics from storage
function loadExcelStats() {
  chrome.storage.local.get(['excelData'], function(result) {
    const excelData = result.excelData || [];
    const productCount = excelData.length;

    // Update product count
    document.getElementById('product-count').textContent = productCount;

    // Update last updated time
    if (productCount > 0) {
      const lastProduct = excelData[excelData.length - 1];
      const lastUpdated = new Date(lastProduct.timestamp).toLocaleDateString();
      document.getElementById('last-updated').textContent = lastUpdated;
    } else {
      document.getElementById('last-updated').textContent = 'Never';
    }

    // Update status
    updateExcelStatus(productCount);
  });
}

// Update Excel status display
function updateExcelStatus(productCount) {
  const statusIndicator = document.getElementById('status-indicator');
  const statusMessage = document.getElementById('status-message');

  if (productCount > 0) {
    statusIndicator.textContent = '✅';
    statusMessage.textContent = `${productCount} products ready`;
    statusMessage.style.color = '#4CAF50';
  } else {
    statusIndicator.textContent = '📋';
    statusMessage.textContent = 'Ready to collect data';
    statusMessage.style.color = '#666';
  }
}

// Set up event listeners for Excel management
function setupEventListeners() {
  const downloadBtn = document.getElementById('download-excel');
  const clearBtn = document.getElementById('clear-data');

  // Download Excel file
  downloadBtn.addEventListener('click', function() {
    downloadExcelFile();
  });

  // Clear all data
  clearBtn.addEventListener('click', function() {
    clearExcelData();
  });
}

// Download Excel file
function downloadExcelFile() {
  const downloadBtn = document.getElementById('download-excel');

  // Show loading state
  downloadBtn.disabled = true;
  downloadBtn.textContent = '📥 Generating...';

  chrome.storage.local.get(['excelData'], function(result) {
    const excelData = result.excelData || [];

    if (excelData.length === 0) {
      showNotification('No data to download. Add some products first!', 'error');
      downloadBtn.disabled = false;
      downloadBtn.textContent = '📥 Download Excel';
      return;
    }

    // Send message to background script to generate Excel file
    chrome.runtime.sendMessage({
      action: 'generateExcelFile',
      data: excelData
    }, function(response) {
      downloadBtn.disabled = false;
      downloadBtn.textContent = '📥 Download Excel';

      if (response && response.status === 'success') {
        showNotification('Excel file downloaded successfully!', 'success');
      } else {
        const errorMsg = response && response.error ? response.error : 'Failed to generate Excel file';
        showNotification('Download failed: ' + errorMsg, 'error');
      }
    });
  });
}

// Clear all Excel data
function clearExcelData() {
  const clearBtn = document.getElementById('clear-data');

  if (!confirm('Are you sure you want to clear all saved product data? This cannot be undone.')) {
    return;
  }

  // Show loading state
  clearBtn.disabled = true;
  clearBtn.textContent = '🗑️ Clearing...';

  chrome.storage.local.remove(['excelData'], function() {
    clearBtn.disabled = false;
    clearBtn.textContent = '🗑️ Clear Data';

    // Reload stats
    loadExcelStats();
    showNotification('All data cleared successfully!', 'success');
  });
}

// Show notification
function showNotification(message, type = 'info') {
  // Create notification element
  const notification = document.createElement('div');
  notification.className = `notification notification-${type}`;
  notification.textContent = message;

  // Style the notification
  notification.style.cssText = `
    position: fixed;
    top: 10px;
    right: 10px;
    padding: 10px 15px;
    border-radius: 4px;
    color: white;
    font-size: 12px;
    z-index: 10000;
    max-width: 250px;
    word-wrap: break-word;
    animation: slideIn 0.3s ease-out;
  `;

  // Set background color based on type
  switch (type) {
    case 'success':
      notification.style.backgroundColor = '#4CAF50';
      break;
    case 'error':
      notification.style.backgroundColor = '#f44336';
      break;
    default:
      notification.style.backgroundColor = '#2196F3';
  }

  document.body.appendChild(notification);

  // Remove notification after 3 seconds
  setTimeout(() => {
    if (notification.parentNode) {
      notification.parentNode.removeChild(notification);
    }
  }, 3000);
}
