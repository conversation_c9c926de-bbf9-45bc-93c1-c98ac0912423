// Popup script for amazonquick Insights & Comparison

document.addEventListener('DOMContentLoaded', function() {
  // Get status text element
  const statusText = document.getElementById('status-text');

  // Check if we're on an Amazon page
  chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
    const currentTab = tabs[0];

    if (currentTab && currentTab.url) {
      if (currentTab.url.includes('amazon.com')) {
        statusText.textContent = 'Active on this Amazon page. Look for the info icon on product images!';

        // You could add functionality here to communicate with the content script
        // to get information about the current page if needed
      } else {
        statusText.textContent = 'This extension only works on Amazon.com pages.';
      }
    }
  });
});
