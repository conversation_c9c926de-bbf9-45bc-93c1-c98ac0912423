// Background script for affliater

// Excel file generation configuration
const EXCEL_CONFIG = {
  FILENAME: 'amazon_products_data.xlsx',
  SHEET_NAME: 'Amazon Products'
};

// Listen for installation
chrome.runtime.onInstalled.addListener(() => {
  console.log('affliater extension installed');
});

// Listen for messages from content script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'getProductInfo') {
    // In a real implementation, you might fetch additional data from Amazon's API
    // or other sources here if needed
    sendResponse({ status: 'success' });
  } else if (request.action === 'fetchProductPage') {
    // Enhanced fetch with better error handling and timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout

    fetch(request.url, {
      signal: controller.signal,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    })
      .then(response => {
        clearTimeout(timeoutId);
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.text();
      })
      .then(html => {
        if (!html || html.trim().length === 0) {
          throw new Error('Empty response received');
        }
        sendResponse({ status: 'success', html: html });
      })
      .catch(error => {
        clearTimeout(timeoutId);
        let errorMessage = 'Unknown error';

        if (error.name === 'AbortError') {
          errorMessage = 'Request timeout - page took too long to load';
        } else if (error.message.includes('Failed to fetch')) {
          errorMessage = 'Network error - unable to reach Amazon';
        } else if (error.message.includes('CORS')) {
          errorMessage = 'Cross-origin request blocked';
        } else {
          errorMessage = error.message || error.toString();
        }

        console.error('Error fetching product page:', {
          url: request.url,
          error: errorMessage,
          originalError: error
        });

        sendResponse({
          status: 'error',
          message: errorMessage,
          url: request.url
        });
      });
    return true; // Keep the message channel open for async responses
  } else if (request.action === 'log') {
    // Log messages from content script
    console.log('Content script log:', request.message);
    sendResponse({ status: 'success' });
  } else if (request.action === 'openTab') {
    // Open a new tab with the specified URL
    chrome.tabs.create({ url: request.url }, (tab) => {
      sendResponse({ status: 'success', tabId: tab.id });
    });
    return true; // Keep the message channel open for async response
  } else if (request.action === 'generateExcelFile') {
    // Generate and download Excel file
    generateExcelFile(request.data)
      .then(result => {
        sendResponse({
          status: 'success',
          result: result
        });
      })
      .catch(error => {
        console.error('Error generating Excel file:', error);
        sendResponse({
          status: 'error',
          error: error.message
        });
      });
    return true; // Keep the message channel open for async response
  }
  return true; // Keep the message channel open for async responses
});

// Note: We've removed the addToCart function since we're now using direct links

// Helper function to inject the content script into Amazon pages
function injectContentScript(tabId, url) {
  // Additional safety check before injection
  if (!isValidAmazonUrl(url)) {
    console.log('Skipping injection for non-Amazon URL:', url);
    return;
  }

  chrome.scripting.executeScript({
    target: { tabId: tabId },
    files: ['content.js']
  }).catch(err => {
    // Only log actual errors, not expected restrictions
    if (!err.message.includes('Cannot access a chrome://') &&
        !err.message.includes('Cannot access chrome://') &&
        !err.message.includes('chrome-extension://')) {
      console.error('Error injecting content script:', err);
    }
  });
}

// List of all Amazon domains
const amazonDomains = [
  'amazon.com', 'amazon.co.uk', 'amazon.ca', 'amazon.de', 'amazon.fr',
  'amazon.it', 'amazon.es', 'amazon.co.jp', 'amazon.in', 'amazon.com.au',
  'amazon.com.mx', 'amazon.com.br', 'amazon.nl', 'amazon.se', 'amazon.pl',
  'amazon.sg', 'amazon.ae', 'amazon.sa', 'amazon.eg', 'amazon.com.tr',
  'amazon.cn', 'amazon.com.be', 'amazon.be'
];

// Function to check if URL is a valid Amazon domain (improved validation)
function isValidAmazonUrl(url) {
  if (!url || typeof url !== 'string') return false;

  try {
    const urlObj = new URL(url);

    // Check for restricted schemes
    const restrictedSchemes = ['chrome:', 'chrome-extension:', 'moz-extension:', 'edge:', 'about:'];
    if (restrictedSchemes.some(scheme => url.startsWith(scheme))) {
      return false;
    }

    // Must be https
    if (urlObj.protocol !== 'https:') {
      return false;
    }

    // Check if hostname contains any Amazon domain
    const hostname = urlObj.hostname.toLowerCase();
    return amazonDomains.some(domain => hostname.includes(domain));
  } catch (error) {
    console.error('Error parsing URL:', url, error);
    return false;
  }
}

// Legacy function for backward compatibility
function isAmazonDomain(url) {
  return isValidAmazonUrl(url);
}

// Listen for tab updates to inject content script when needed
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  // Only proceed if tab loading is complete and we have a valid URL
  if (changeInfo.status === 'complete' && tab.url && isValidAmazonUrl(tab.url)) {
    console.log('Injecting content script into Amazon page:', tab.url);
    injectContentScript(tabId, tab.url);
  }
});

// Function to generate Excel file from product data
async function generateExcelFile(productData) {
  try {
    // Create CSV content (Excel can open CSV files)
    const csvContent = generateCSVContent(productData);

    // Create a blob with CSV content
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

    // Create download URL
    const url = URL.createObjectURL(blob);

    // Generate filename with current date
    const currentDate = new Date().toISOString().split('T')[0];
    const filename = `amazon_products_${currentDate}.csv`;

    // Trigger download
    chrome.downloads.download({
      url: url,
      filename: filename,
      saveAs: true
    }, function(downloadId) {
      if (chrome.runtime.lastError) {
        throw new Error(chrome.runtime.lastError.message);
      }
      console.log('Excel file download started:', downloadId);

      // Clean up the URL after download starts
      setTimeout(() => {
        URL.revokeObjectURL(url);
      }, 1000);
    });

    return { downloadId: 'success', filename: filename };

  } catch (error) {
    console.error('Error generating Excel file:', error);
    throw error;
  }
}

// Function to generate CSV content from product data
function generateCSVContent(productData) {
  // Define CSV headers
  const headers = [
    'Timestamp',
    'Product Title',
    'ASIN',
    'Current Price',
    'Old Price',
    'Discount',
    'Rating',
    'Review Count',
    'Prime Eligible',
    'Product URL',
    'About Item',
    'Description',
    'Specifications',
    'Shipping',
    'Image URL',
    'Total Images',
    'Top Review'
  ];

  // Create CSV content
  let csvContent = headers.join(',') + '\n';

  // Add data rows
  productData.forEach(product => {
    const row = [
      escapeCSVField(product.timestamp),
      escapeCSVField(product.title),
      escapeCSVField(product.asin),
      escapeCSVField(product.currentPrice),
      escapeCSVField(product.oldPrice),
      escapeCSVField(product.discount),
      escapeCSVField(product.rating),
      escapeCSVField(product.reviewCount),
      escapeCSVField(product.isPrime),
      escapeCSVField(product.productUrl),
      escapeCSVField(product.aboutItem),
      escapeCSVField(product.description),
      escapeCSVField(product.specifications),
      escapeCSVField(product.shipping),
      escapeCSVField(product.imageUrl),
      escapeCSVField(product.totalImages),
      escapeCSVField(product.topReview)
    ];

    csvContent += row.join(',') + '\n';
  });

  return csvContent;
}

// Function to escape CSV fields (handle commas, quotes, newlines)
function escapeCSVField(field) {
  if (field === null || field === undefined) {
    return '""';
  }

  const stringField = String(field);

  // If field contains comma, quote, or newline, wrap in quotes and escape internal quotes
  if (stringField.includes(',') || stringField.includes('"') || stringField.includes('\n') || stringField.includes('\r')) {
    return '"' + stringField.replace(/"/g, '""') + '"';
  }

  return stringField;
}


